
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Trabalho Desenvolvimento Mobile - HamburgueriaZ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
        }
        h3 {
            color: #7f8c8d;
        }
        .info-box {
            background-color: #ecf0f1;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        .center {
            text-align: center;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>

<h1>TRABALHO PRÁTICO - DESENVOLVIMENTO MOBILE</h1>
<h1>APLICATIVO HAMBURGUERIAZ</h1>

<div class="info-box">
    <p><strong>Disciplina:</strong> Desenvolvimento Mobile</p>
    <p><strong>Objetivo:</strong> Desenvolver aplicativo Android completo seguindo roteiro de aula prática</p>
    <p><strong>Linguagem:</strong> Java</p>
    <p><strong>IDE:</strong> Android Studio</p>
    <p><strong>Data:</strong> 2024</p>
</div>

<h2>SUMÁRIO</h2>
<ol>
    <li>Introdução</li>
    <li>Objetivos</li>
    <li>Infraestrutura Utilizada</li>
    <li>Desenvolvimento do Projeto</li>
    <li>Implementação das Funcionalidades</li>
    <li>Resultados Obtidos</li>
    <li>Conclusão</li>
    <li>Anexos - Código Fonte</li>
</ol>

<div class="page-break"></div>

<h2>1. INTRODUÇÃO</h2>
<p>Este trabalho apresenta o desenvolvimento completo de um aplicativo Android para uma hamburgueria fictícia chamada "HamburgueriaZ". O projeto foi desenvolvido seguindo um roteiro estruturado de aula prática, implementando desde a interface básica até funcionalidades avançadas como integração com aplicativos externos através de Intents.</p>

<p>O aplicativo permite que clientes realizem pedidos de hambúrgueres diretamente pelo smartphone, escolhendo adicionais, quantidade e enviando o pedido por e-mail, eliminando a necessidade de aplicativos de terceiros.</p>

<h2>2. OBJETIVOS</h2>

<h3>2.1 Objetivos Gerais</h3>
<ul>
    <li>Desenvolver competências em desenvolvimento mobile para Android</li>
    <li>Aplicar conceitos de interface de usuário (UI) e experiência do usuário (UX)</li>
    <li>Implementar funcionalidades em Java para Android</li>
    <li>Utilizar Intents para integração com aplicativos externos</li>
</ul>

<h3>2.2 Objetivos Específicos</h3>
<ul>
    <li>Saber utilizar o software Android Studio</li>
    <li>Construir interface de aplicação Android com estilos predefinidos e imagens</li>
    <li>Implementar funcionalidades de um app na linguagem Java</li>
    <li>Utilizar Intents para interação com aplicativos externos</li>
    <li>Exportar projeto do Android Studio</li>
</ul>

<h2>3. INFRAESTRUTURA UTILIZADA</h2>

<h3>3.1 Software Principal</h3>
<div class="info-box">
    <p><strong>Android Studio Flamingo | 2022.2.1</strong></p>
    <ul>
        <li>Ambiente de desenvolvimento integrado (IDE) oficial para desenvolvimento Android</li>
        <li>Baseado no IntelliJ IDEA</li>
        <li>Ferramentas avançadas de desenvolvimento</li>
        <li>Editor de código com recursos de autocomplete e debugging</li>
    </ul>
</div>

<h3>3.2 Pré-requisitos</h3>
<div class="info-box">
    <p><strong>Java JDK 20</strong></p>
    <ul>
        <li>Ambiente de desenvolvimento para construção de aplicativos Java</li>
        <li>Necessário para compilação do código Android</li>
    </ul>
</div>

<h3>3.3 Configurações do Projeto</h3>
<ul>
    <li>SDK Mínimo: API 23 (Android 6.0)</li>
    <li>SDK de Compilação: API 34 (Android 14)</li>
    <li>Linguagem: Java</li>
    <li>Tipo de Licença: Freeware</li>
</ul>

<h2>4. DESENVOLVIMENTO DO PROJETO</h2>

<h3>ETAPA 1: CRIAÇÃO DO PROJETO</h3>
<p>O projeto foi iniciado no Android Studio seguindo os seguintes passos:</p>

<div class="info-box">
    <p><strong>1. Criação do Novo Projeto</strong></p>
    <ul>
        <li>File > New > New Project</li>
        <li>Template: Empty Activity</li>
        <li>Nome: HamburgueriaZ</li>
        <li>Linguagem: Java</li>
        <li>SDK Mínimo: API 23</li>
    </ul>
</div>

<h3>ETAPA 2: DESENVOLVIMENTO DA INTERFACE INICIAL</h3>
<p>A interface foi desenvolvida com foco na usabilidade e experiência do usuário, implementando todos os requisitos especificados:</p>

<h4>Componentes Implementados:</h4>
<ol>
    <li><strong>Campo Nome do Cliente</strong>
        <ul>
            <li>EditText para entrada de texto</li>
            <li>Validação obrigatória</li>
            <li>Hint explicativo</li>
        </ul>
    </li>
    <li><strong>Seleção de Adicionais</strong>
        <ul>
            <li>CheckBox para Bacon (+R$ 2,00)</li>
            <li>CheckBox para Queijo (+R$ 2,00)</li>
            <li>CheckBox para Onion Rings (+R$ 3,00)</li>
            <li>Atualização automática do preço</li>
        </ul>
    </li>
    <li><strong>Controle de Quantidade</strong>
        <ul>
            <li>Botão "-" para diminuir</li>
            <li>TextView central mostrando quantidade</li>
            <li>Botão "+" para aumentar</li>
            <li>Proteção contra valores negativos</li>
        </ul>
    </li>
    <li><strong>Exibição de Informações</strong>
        <ul>
            <li>TextView para preço total</li>
            <li>TextView para resumo do pedido</li>
            <li>Atualização em tempo real</li>
        </ul>
    </li>
    <li><strong>Ação Principal</strong>
        <ul>
            <li>Botão "Enviar Pedido"</li>
            <li>Integração com Intent de e-mail</li>
        </ul>
    </li>
</ol>

<h3>ETAPA 3: PADRONIZAÇÃO DE ESTILOS</h3>
<p>Foi criado um estilo personalizado para padronizar a aparência dos textos:</p>

<div class="code">
<strong>Estilo "EstiloTexto" Implementado:</strong><br>
• layout_width e layout_height: wrap_content<br>
• gravity: center_vertical<br>
• textAllCaps: true<br>
• textSize: 15sp<br>
• paddingTop e paddingBottom: 16dp<br>
• textStyle: bold<br>
• textColor: #333333
</div>

<h3>ETAPA 4: ADIÇÃO DE IMAGENS</h3>
<p>Foi implementada uma ImageView no topo da aplicação funcionando como banner:</p>
<ul>
    <li>Logo vetorial personalizada da HamburgueriaZ</li>
    <li>Posicionamento no topo da tela</li>
    <li>Estilo de banner responsivo</li>
    <li>Cores harmoniosas com o tema do app</li>
</ul>

<div class="page-break"></div>

<h2>5. IMPLEMENTAÇÃO DAS FUNCIONALIDADES</h2>

<h3>ETAPA 5: FUNCIONALIDADES DE QUANTIDADE</h3>
<div class="info-box">
    <p><strong>Métodos Implementados:</strong></p>
    <ul>
        <li><strong>somar():</strong> Incrementa quantidade e atualiza preço</li>
        <li><strong>subtrair():</strong> Decrementa quantidade (mínimo 1) e atualiza preço</li>
    </ul>
    
    <p><strong>Características:</strong></p>
    <ul>
        <li>Proteção contra valores negativos</li>
        <li>Atualização automática do preço</li>
        <li>Feedback visual para o usuário</li>
    </ul>
</div>

<h3>ETAPA 6: FUNÇÃO ENVIAR PEDIDO</h3>
<div class="info-box">
    <p><strong>Funcionalidades Implementadas:</strong></p>
    <ol>
        <li><strong>Identificação do Cliente</strong>
            <ul>
                <li>Captura do nome digitado</li>
                <li>Validação de campo obrigatório</li>
            </ul>
        </li>
        <li><strong>Verificação de Adicionais</strong>
            <ul>
                <li>Verificação do estado dos CheckBoxes</li>
                <li>Identificação dos itens selecionados</li>
            </ul>
        </li>
        <li><strong>Cálculo do Preço Total</strong>
            <ul>
                <li>Preço base: R$ 20,00</li>
                <li>Bacon: +R$ 2,00</li>
                <li>Queijo: +R$ 2,00</li>
                <li>Onion Rings: +R$ 3,00</li>
                <li>Multiplicação pela quantidade</li>
            </ul>
        </li>
        <li><strong>Geração do Resumo</strong>
            <ul>
                <li>Nome do cliente</li>
                <li>Adicionais selecionados (Sim/Não)</li>
                <li>Quantidade</li>
                <li>Preço final formatado</li>
            </ul>
        </li>
    </ol>
</div>

<h3>ETAPA 7: IMPLEMENTAÇÃO DE INTENTS</h3>
<div class="info-box">
    <p><strong>Intent para E-mail implementado com as seguintes características:</strong></p>
    <ul>
        <li>Tipo: ACTION_SENDTO</li>
        <li>Protocolo: mailto:</li>
        <li>Assunto automático: "Pedido de [nome do cliente]"</li>
        <li>Corpo: Resumo completo do pedido</li>
        <li>Verificação de disponibilidade de apps de e-mail</li>
        <li>Tratamento de erro quando não há app disponível</li>
    </ul>
    
    <p><strong>Funcionalidade:</strong><br>
    Ao clicar em "Enviar Pedido", o aplicativo abre automaticamente o app de e-mail padrão do dispositivo com o assunto e corpo já preenchidos com as informações do pedido.</p>
</div>

<h2>6. RESULTADOS OBTIDOS</h2>

<h3>6.1 Funcionalidades Implementadas com Sucesso</h3>
<ul>
    <li><span class="success">✅ Interface Completa</span> - Layout responsivo e intuitivo</li>
    <li><span class="success">✅ Lógica de Negócio</span> - Cálculo correto de preços e validações</li>
    <li><span class="success">✅ Integração Externa</span> - Intent funcionando corretamente</li>
    <li><span class="success">✅ Experiência do Usuário</span> - Feedback visual adequado</li>
</ul>

<h3>6.2 Testes Realizados</h3>
<ol>
    <li><strong>Teste de Interface</strong> - Todos os componentes respondem adequadamente</li>
    <li><strong>Teste de Funcionalidades</strong> - Cálculo de preços e controle de quantidade</li>
    <li><strong>Teste de Integração</strong> - Intent abre aplicativo de e-mail corretamente</li>
</ol>

<h2>7. CONCLUSÃO</h2>
<p>O desenvolvimento do aplicativo HamburgueriaZ foi concluído com sucesso, atendendo a todos os objetivos propostos no roteiro de aula prática. O projeto demonstrou a aplicação prática de conceitos fundamentais do desenvolvimento Android.</p>

<h3>Competências Desenvolvidas:</h3>
<ol>
    <li><strong>Desenvolvimento de Interface</strong>
        <ul>
            <li>Criação de layouts responsivos</li>
            <li>Aplicação de estilos personalizados</li>
            <li>Uso adequado de componentes Android</li>
        </ul>
    </li>
    <li><strong>Programação em Java para Android</strong>
        <ul>
            <li>Implementação de lógica de negócio</li>
            <li>Manipulação de eventos</li>
            <li>Gerenciamento de estado da aplicação</li>
        </ul>
    </li>
    <li><strong>Integração com Sistema</strong>
        <ul>
            <li>Uso de Intents para comunicação entre apps</li>
            <li>Tratamento de casos de erro</li>
            <li>Validação de disponibilidade de recursos</li>
        </ul>
    </li>
    <li><strong>Boas Práticas</strong>
        <ul>
            <li>Código bem estruturado e comentado</li>
            <li>Separação de responsabilidades</li>
            <li>Tratamento adequado de exceções</li>
        </ul>
    </li>
</ol>

<h3>Resultados Alcançados:</h3>
<ul>
    <li><span class="success">✅ Aplicativo funcional e completo</span></li>
    <li><span class="success">✅ Interface intuitiva e atrativa</span></li>
    <li><span class="success">✅ Todas as funcionalidades implementadas</span></li>
    <li><span class="success">✅ Integração externa funcionando</span></li>
    <li><span class="success">✅ Código bem documentado</span></li>
    <li><span class="success">✅ Projeto exportável</span></li>
</ul>

<p>O aplicativo HamburgueriaZ representa uma solução completa para pedidos de hamburgueria, demonstrando como conceitos teóricos de desenvolvimento mobile podem ser aplicados na prática para resolver problemas reais de negócio.</p>

<div class="page-break"></div>

<h2>8. ANEXOS - ESTRUTURA DO PROJETO</h2>

<div class="code">
<strong>Estrutura Final do Projeto:</strong><br><br>
HamburgueriaZ/<br>
├── app/<br>
│   ├── src/main/<br>
│   │   ├── java/com/example/hamburgueriaz/<br>
│   │   │   └── MainActivity.java (200+ linhas)<br>
│   │   ├── res/<br>
│   │   │   ├── layout/<br>
│   │   │   │   └── activity_main.xml<br>
│   │   │   ├── values/<br>
│   │   │   │   ├── colors.xml<br>
│   │   │   │   ├── strings.xml<br>
│   │   │   │   └── themes.xml<br>
│   │   │   └── drawable/<br>
│   │   │       └── logo_hamburgueria.xml<br>
│   │   └── AndroidManifest.xml<br>
│   └── build.gradle<br>
├── build.gradle<br>
├── settings.gradle<br>
├── gradle.properties<br>
└── README.md
</div>

<div class="info-box">
    <p><strong>Arquivos Principais Criados:</strong></p>
    <ul>
        <li><strong>MainActivity.java</strong> - Lógica principal da aplicação</li>
        <li><strong>activity_main.xml</strong> - Layout da interface</li>
        <li><strong>themes.xml</strong> - Estilos personalizados</li>
        <li><strong>AndroidManifest.xml</strong> - Configurações do app</li>
        <li><strong>build.gradle</strong> - Dependências e configurações de build</li>
    </ul>
</div>

<div class="center" style="margin-top: 50px; border-top: 2px solid #3498db; padding-top: 20px;">
    <p><strong>Trabalho desenvolvido seguindo roteiro de aula prática de Desenvolvimento Mobile</strong></p>
    <p><strong>Todas as etapas foram implementadas com sucesso</strong></p>
    <p><strong>Projeto pronto para uso e expansão futura</strong></p>
</div>

</body>
</html>
    