<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>A Tela Principal do VisuAlg</TITLE>
	<META NAME="GENERATOR" CONTENT="BrOffice.org 3.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="<PERSON>">
	<META NAME="CHANGED" CONTENT="20150711;20014800">
	<META NAME="Originator" CONTENT="Microsoft Word 12">
	<META NAME="ProgId" CONTENT="Word.Document">
	<!--[if !mso]>
<style>
v\:* {behavior:url(#default#VML);}
o\:* {behavior:url(#default#VML);}
w\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style>
<![endif]-->
	<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>Cliente</o:Author>
  <o:Template>Normal</o:Template>
  <o:LastAuthor>Cliente</o:LastAuthor>
  <o:Revision>4</o:Revision>
  <o:TotalTime>11</o:TotalTime>
  <o:Created>2015-06-22T04:20:00Z</o:Created>
  <o:LastSaved>2015-06-22T04:31:00Z</o:LastSaved>
  <o:Pages>2</o:Pages>
  <o:Words>1117</o:Words>
  <o:Characters>6033</o:Characters>
  <o:Company>Home</o:Company>
  <o:Lines>50</o:Lines>
  <o:Paragraphs>14</o:Paragraphs>
  <o:CharactersWithSpaces>7136</o:CharactersWithSpaces>
  <o:Version>12.00</o:Version>
 </o:DocumentProperties>
</xml><![endif]-->
	<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:HyphenationZone>21</w:HyphenationZone>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>PT-BR</w:LidThemeOther>
  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:DontVertAlignCellWithSp/>
   <w:DontBreakConstrainedForcedTables/>
   <w:DontVertAlignInTxbx/>
   <w:Word11KerningPairs/>
   <w:CachedColBalance/>
  </w:Compatibility>
  <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="--"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]-->
	<!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="true"
  DefSemiHidden="true" DefQFormat="false" DefPriority="99"
  LatentStyleCount="267">
  <w:LsdException Locked="false" Priority="0" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 1"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 2"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 3"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 4"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 5"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 6"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 7"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 8"/>
  <w:LsdException Locked="false" Priority="39" Name="toc 9"/>
  <w:LsdException Locked="false" Priority="35" QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" Priority="10" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" Priority="1" Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" Priority="11" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" Priority="22" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" Priority="20" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" Priority="59" SemiHidden="false"
   UnhideWhenUsed="false" Name="Table Grid"/>
  <w:LsdException Locked="false" UnhideWhenUsed="false" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" UnhideWhenUsed="false" Name="Revision"/>
  <w:LsdException Locked="false" Priority="34" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="29" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="30" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" SemiHidden="false"
   UnhideWhenUsed="false" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" SemiHidden="false"
   UnhideWhenUsed="false" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="false"
   UnhideWhenUsed="false" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="false"
   UnhideWhenUsed="false" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" SemiHidden="false"
   UnhideWhenUsed="false" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" QFormat="true" Name="TOC Heading"/>
 </w:LatentStyles>
</xml><![endif]-->
	<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Tabela normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-qformat:yes;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-para-margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman","serif";}
</style>
<![endif]-->
	<META HTTP-EQUIV="Content-Language" CONTENT="pt-br">
	<!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="2050"/>
</xml><![endif]-->
	<!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
	<STYLE TYPE="text/css">
	<!--
		P { color: #000000 }
		A:visited { color: #800080 }
		A:link { color: #0000ff }
	-->
	</STYLE>
</HEAD>
<BODY LANG="pt-BR" TEXT="#000000" LINK="#0000ff" VLINK="#800080" DIR="LTR">
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=3 STYLE="font-size: 13pt">A
Tela Principal do VISUALG 3.01 (Pl&aacute;stico)</FONT></FONT></P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2>A tela do Visualg
comp&otilde;e-se da barra de tarefas, do editor de textos (que toma
toda a sua metade superior), do quadro de vari&aacute;veis (no lado
esquerdo da metade inferior), do simulador de sa&iacute;da (no
correspondente lado direito) e da barra de <I>status</I>. Quando o
programa &eacute; carregado, j&aacute; apresenta no editor um
&quot;esqueleto&quot; de pseudoc&oacute;digo, com a inten&ccedil;&atilde;o
de poupar trabalho ao usu&aacute;rio e de mostrar o formato b&aacute;sico
que deve ser seguido. Explicaremos a seguir cada componente da
interface do VisuAlg.</FONT></FONT></P>
<P ALIGN=CENTER><IMG SRC="../../VISUALG305/imagens/TELA_PRINCIPAL2.PNG" NAME="figura1" ALIGN=LEFT WIDTH=1140 HEIGHT=644 BORDER=0><BR CLEAR=LEFT><!--[if gte vml 1]><v:shapetype
 id="_x0000_t75" coordsize="21600,21600" o:spt="75" o:preferrelative="t"
 path="m@4@5l@4@11@9@11@9@5xe" filled="f" stroked="f">
 <v:stroke joinstyle="miter"/>
 <v:formulas>
  <v:f eqn="if lineDrawn pixelLineWidth 0"/>
  <v:f eqn="sum @0 1 0"/>
  <v:f eqn="sum 0 0 @1"/>
  <v:f eqn="prod @2 1 2"/>
  <v:f eqn="prod @3 21600 pixelWidth"/>
  <v:f eqn="prod @3 21600 pixelHeight"/>
  <v:f eqn="sum @0 0 1"/>
  <v:f eqn="prod @6 1 2"/>
  <v:f eqn="prod @7 21600 pixelWidth"/>
  <v:f eqn="sum @8 21600 0"/>
  <v:f eqn="prod @7 21600 pixelHeight"/>
  <v:f eqn="sum @10 21600 0"/>
 </v:formulas>
 <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
 <o:lock v:ext="edit" aspectratio="t"/>
</v:shapetype><v:shape id="Imagem_x0020_9" o:spid="_x0000_i1029" type="#_x0000_t75"
 style='width:805.5pt;height:455.25pt;visibility:visible;mso-wrap-style:square'>
 <v:imagedata src="telaprin_arquivos/image001.png" o:title=""/>
</v:shape><![endif]-->&nbsp;</P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>A Barra de Tarefas&nbsp;
(mudou) </B></FONT></FONT>
</P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Cont&eacute;m os
comandos mais utilizados no VisuAlg (estes comandos tamb&eacute;m
podem ser acessados pelo menu ou por atalhos no teclado).</FONT></FONT></P>
<P ALIGN=CENTER><A NAME="_x0000_i1027"></A><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_MADEIRA.PNG" NAME="figura10" ALIGN=LEFT WIDTH=1140 HEIGHT=69 BORDER=0><BR CLEAR=LEFT><!--[if gte vml 1]><v:shape
 id="Imagem_x0020_13" o:spid="_x0000_i1028" type="#_x0000_t75" style='width:771.75pt;
 height:37.5pt;visibility:visible;mso-wrap-style:square'>
 <v:imagedata src="telaprin_arquivos/image004.png" o:title=""/>
</v:shape><![endif]--><IMG SRC="barra1.jpg" NAME="figura3" ALIGN=BOTTOM WIDTH=638 HEIGHT=181 BORDER=0></P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Abrir (Ctrl-A):</B></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>Abre um arquivo
anteriormente gravado, substituindo o texto presente no editor. Se
este tiver sido modificado, o VisuAlg pedir&aacute; sua confirma&ccedil;&atilde;o
para salv&aacute;-lo antes que seja sobreposto.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Novo
(Ctrl-N):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Cria
um novo &quot;esqueleto&quot; de pseudoc&oacute;digo, substituindo o
texto presente no editor. Se este tiver sido modificado, o VisuAlg
pedir&aacute; sua confirma&ccedil;&atilde;o para salv&aacute;-lo
antes que seja sobreposto.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Salvar
(Ctrl-S):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Grava
imediatamente o texto presente no editor. Na primeira vez que um novo
texto &eacute; gravado, o VisuAlg pede seu nome e
localiza&ccedil;&atilde;o.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Imprimir:</B></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>Imprime imediatamente na
impressora padr&atilde;o o texto presente no editor. Para configurar
a impress&atilde;o, use o comando Imprimir do menu Arquivo (acess&iacute;vel
tamb&eacute;m pelo atalho </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>Ctrl-P</I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>).<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Cortar
(Ctrl-X):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Apaga
texto selecionado, armazenando-o em uma &aacute;rea de
transfer&ecirc;ncia.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Copiar
(Ctrl-C):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Copia
o texto selecionado para a &aacute;rea de transfer&ecirc;ncia.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Colar
(Ctrl-V):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Copia
texto da &aacute;rea de transfer&ecirc;ncia para o local em que est&aacute;
o cursor.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Gravar
bloco de texto:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Permite
a grava&ccedil;&atilde;o em arquivo de um texto selecionado no
editor. A extens&atilde;o sugerida para o nome do arquivo &eacute;
</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>.inc</I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Inserir
bloco de texto:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Permite
a inser&ccedil;&atilde;o do conte&uacute;do de um arquivo. A extens&atilde;o
sugerida para o nome do arquivo &eacute; </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>.inc</I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Desfazer
(Ctrl-Z):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Desfaz
&uacute;ltimo comando efetuado.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Refazer
(Shift-Ctrl-Z):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Refaz
&uacute;ltimo comando desfeito.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Localizar
(Ctrl-L):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Localiza
no texto presente no editor determinada palavra
especificada.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Substituir
(Ctrl-U):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Localiza
no texto presente no editor determinada palavra especificada,
substituindo-a por outra.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Corrigir
Indenta&ccedil;&atilde;o (Ctrl-G)</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>:
Corrige automaticamente a </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>indenta&ccedil;&atilde;o</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>(ou tabula&ccedil;&atilde;o)
do pseudoc&oacute;digo, tabulando cada comando interno com espa&ccedil;os
&agrave; esquerda.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Numerar
linhas:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Ativa
ou desativa a exibi&ccedil;&atilde;o dos n&uacute;meros das linhas na
&aacute;rea &agrave; esquerda do editor. A linha e a coluna do editor
em que o cursor est&aacute; em um determinado momento tamb&eacute;m
s&atilde;o mostradas na barra de </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>status</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>(parte inferior da tela).
Por motivos t&eacute;cnicos, esta op&ccedil;&atilde;o &eacute;
automaticamente desativada durante a execu&ccedil;&atilde;o do
pseudoc&oacute;digo, mas volta a ser ativada logo em seguida.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Mostrar
vari&aacute;veis modificadas</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>:
Ativa ou desativa a exibi&ccedil;&atilde;o da vari&aacute;vel que
est&aacute; sendo modificada. Como o n&uacute;mero de vari&aacute;veis
pode ser grande, muitas podem estar fora da janela de visualiza&ccedil;&atilde;o;
quando esta caracter&iacute;stica est&aacute; ativada, o VisuAlg rola
a grade de exibi&ccedil;&atilde;o de modo que cada vari&aacute;vel
fique vis&iacute;vel no momento em est&aacute; sendo modificada. Este
recurso &eacute; especialmente &uacute;til quando se executa um
pseudoc&oacute;digo passo a passo. Por quest&otilde;es de desempenho,
a configura&ccedil;&atilde;o padr&atilde;o desta caracter&iacute;stica
&eacute; </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>desativada,</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>quando o pseudoc&oacute;digo
est&aacute; sendo executado automaticamente. No entanto, basta clicar
este bot&atilde;o para execut&aacute;-lo automaticamente com a
exibi&ccedil;&atilde;o ativada. No final da execu&ccedil;&atilde;o, a
configura&ccedil;&atilde;o volta a ser </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>desativada</I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>.</FONT></FONT></P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_MADEIRA2.PNG" NAME="figura6" ALIGN=LEFT WIDTH=1140 HEIGHT=78 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><A NAME="_x0000_i1025"></A><!--[if gte vml 1]><v:shape id="Imagem_x0020_12"
 o:spid="_x0000_i1026" type="#_x0000_t75" style='width:771.75pt;height:37.5pt;
 visibility:visible;mso-wrap-style:square'>
 <v:imagedata src="telaprin_arquivos/image004.png" o:title=""/>
</v:shape><![endif]--><IMG SRC="barra2.jpg" NAME="figura5" ALIGN=BOTTOM WIDTH=697 HEIGHT=189 BORDER=0></P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_METAL.PNG" NAME="figura7" ALIGN=LEFT WIDTH=1140 HEIGHT=81 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_NENHUM.PNG" NAME="figura2" ALIGN=LEFT WIDTH=1140 HEIGHT=75 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_PLASTICO.PNG" NAME="figura8" ALIGN=LEFT WIDTH=1140 HEIGHT=77 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_VISUALG3_AGUA.PNG" NAME="figura12" ALIGN=LEFT WIDTH=1140 HEIGHT=80 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Executar
(F9):&nbsp;</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Inicia
(ou continua) a execu&ccedil;&atilde;o autom&aacute;tica do
pseudoc&oacute;digo.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Executar
com </B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I><B>timer</B></I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>(Shift-F9):</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>:
Insere um atraso (que pode ser especificado no intervalo ao lado)
antes da execu&ccedil;&atilde;o de cada linha. Tamb&eacute;m real&ccedil;a
em fundo azul o comando que est&aacute; sendo executado, da mesma
forma que na execu&ccedil;&atilde;o passo a passo.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Intervalo
do </B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I><B>timer</B></I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>:&nbsp;</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Atraso
em cada linha, para quando se deseja executar o pseudoc&oacute;digo
com </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>timer</I></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2>.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Passo
(F8):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Inicia
(ou continua) a execu&ccedil;&atilde;o linha por linha do
pseudoc&oacute;digo, dando ao usu&aacute;rio a oportunidade de
acompanhar o fluxo de execu&ccedil;&atilde;o, os valores das
vari&aacute;veis e a pilha de ativa&ccedil;&atilde;o dos
subprogramas.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Parar
(Ctrl-F2):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Termina
imediatamente a execu&ccedil;&atilde;o do pseudoc&oacute;digo.
Evidentemente, este bot&atilde;o fica desabilitado quando o
pseudoc&oacute;digo n&atilde;o est&aacute; sendo
executado.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Liga/desliga
</B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I><B>breakpoint</B></I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>(F5):</B></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>Insere/remove um ponto de
parada na linha em que esteja o cursor. Estes pontos de parada s&atilde;o
&uacute;teis para a depura&ccedil;&atilde;o e acompanhamento da
execu&ccedil;&atilde;o dos pseudoc&oacute;digos, pois permitem a
verifica&ccedil;&atilde;o dos valores das vari&aacute;veis e da pilha
de ativa&ccedil;&atilde;o de subprogramas. </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Desmarcar
todos os </B></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I><B>breakpoints</B></I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>(Ctrl-F5):</B></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>Desativa todos os
</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>breakpoints</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>que estejam ativados
naquele momento. </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Executar
em modo DOS:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Com
esta op&ccedil;&atilde;o ativada, tanto a entrada como a sa&iacute;da-padr&atilde;o
passa a ser uma janela que imita o DOS, simulando a execu&ccedil;&atilde;o
de um programa neste ambiente. </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Gerar
valores aleat&oacute;rios:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Ativa
a gera&ccedil;&atilde;o de valores aleat&oacute;rios que substituem a
digita&ccedil;&atilde;o de dados. A faixa padr&atilde;o de valores
gerados &eacute; de 0 a 100 inclusive, mas pode ser modificada (basta
alterar intervalo ao lado). Para a gera&ccedil;&atilde;o de dados do
tipo caractere, n&atilde;o h&aacute; uma faixa pr&eacute;-estabelecida:
os dados gerados ser&atilde;o sempre </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>strings</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>de 5 letras
mai&uacute;sculas.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Intervalo
dos valores aleat&oacute;rios:</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Faixa
de valores que ser&atilde;o gerados automaticamente, quando esta
op&ccedil;&atilde;o estiver ativada.<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Perfil
(F7):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Ap&oacute;s
a execu&ccedil;&atilde;o de um pseudoc&oacute;digo, exibe o n&uacute;mero
de vezes que cada umas das suas linhas foi executada. &Eacute; &uacute;til
para a an&aacute;lise de efici&ecirc;ncia (por exemplo, nos m&eacute;todos
de ordena&ccedil;&atilde;o).<BR></FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Mostrar
pilha de ativa&ccedil;&atilde;o (Ctrl-F3):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Exibe
a pilha de subprogramas ativados num dado momento. Conv&eacute;m
utilizar este comando em conjunto com </FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><I>breakpoints</I></FONT></FONT>
<FONT FACE="Arial, sans-serif"><FONT SIZE=2>ou com a execu&ccedil;&atilde;o
passo a passo.</FONT></FONT><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B><BR>Ajuda
(F1):</B></FONT></FONT> <FONT FACE="Arial, sans-serif"><FONT SIZE=2>Possibilita
acesso &agrave;s p&aacute;ginas de ajuda e &agrave;s informa&ccedil;&otilde;es
sobre o VisuAlg.</FONT></FONT></P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>Quadro de Vari&aacute;veis</B></FONT></FONT></P>
<P><IMG SRC="../../VISUALG305/imagens/TELA_MADEIRA_VARI%C3%81VEIS.PNG" NAME="figura4" ALIGN=LEFT WIDTH=560 HEIGHT=234 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2>&Eacute; formado por
uma grade na qual s&atilde;o mostrados o escopo de cada vari&aacute;vel
(se for do programa principal, ser&aacute; global; se for local, ser&aacute;
apresentado o nome do subprograma onde foi declarada), seus nomes
(tamb&eacute;m com os &iacute;ndices, nos casos em que sejam
vetores), seu tipo (&quot;I&quot; para inteiro, &quot;R&quot; para
real, &quot;C&quot; para caractere e &quot;L&quot; para l&oacute;gico)
e o seu valor corrente. A vers&atilde;o atual do VisuAlg permite a
visualiza&ccedil;&atilde;o de at&eacute; 500 vari&aacute;veis
(contando individualmente cada elemento dos vetores).</FONT></FONT></P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2><B>A Barra de <I>Status</I></B></FONT></FONT></P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_STATUS3_VISUALG3.PNG" NAME="figura9" ALIGN=LEFT WIDTH=1140 HEIGHT=69 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Situada na parte
inferior da tela, esta barra cont&eacute;m dois pain&eacute;is: o
primeiro mostra a linha e a coluna onde o cursor est&aacute;, e o
segundo mostra a palavra <I>Modificado</I> no caso em que o
pseudoc&oacute;digo tenha sido alterado desde que foi carregado ou
salvo pela &uacute;ltima vez. Nesta barra, h&aacute; ainda um
terceiro painel dispon&iacute;vel, que ainda n&atilde;o tem um uso
espec&iacute;fico na atual vers&atilde;o.</FONT></FONT></P>
<P><IMG SRC="../../VISUALG305/imagens/BARRA_STATUS2_VISUALG3.PNG" NAME="figura11" ALIGN=LEFT WIDTH=1140 HEIGHT=420 BORDER=0><BR CLEAR=LEFT><BR><BR>
</P>
<P><BR><BR>
</P>
<P ALIGN=CENTER>&nbsp; <FONT FACE="Arial, sans-serif"><FONT SIZE=2><A HREF="objetivos.htm">Objetivos</A>&nbsp;&nbsp;&nbsp;
<A HREF="telaprin.htm">Tela principal</A>&nbsp;&nbsp;&nbsp;</FONT></FONT>
<A HREF="menu.htm"><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Menu</FONT></FONT></A>&nbsp;&nbsp;&nbsp;
<A HREF="linguagem.htm"><FONT FACE="Arial, sans-serif"><FONT SIZE=2>A
linguagem do VisuAlg</FONT></FONT></A>&nbsp;&nbsp;&nbsp; <A HREF="refer.htm"><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Refer&ecirc;ncias
da linguagem do VisuAlg</FONT></FONT></A>&nbsp;&nbsp; <A HREF="autocomp.htm"><FONT FACE="Arial, sans-serif"><FONT SIZE=2>Mais
recursos</FONT></FONT></A></P>
</BODY>
</HTML>