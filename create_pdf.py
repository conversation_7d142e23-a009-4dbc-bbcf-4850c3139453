#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
    from reportlab.lib import colors
    from reportlab.graphics.shapes import Drawing, Rect, String
    from reportlab.graphics import renderPDF
    import os
    reportlab_available = True
except ImportError:
    reportlab_available = False

def create_pdf_with_reportlab():
    """Cria PDF completo e profissional usando ReportLab"""
    doc = SimpleDocTemplate("Trabalho_Desenvolvimento_Mobile_HamburgueriaZ_COMPLETO.pdf",
                          pagesize=A4,
                          leftMargin=2*cm,
                          rightMargin=2*cm,
                          topMargin=2*cm,
                          bottomMargin=2*cm)
    styles = getSampleStyleSheet()

    # Estilos personalizados
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        spaceBefore=20,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#2c3e50'),
        fontName='Helvetica-Bold'
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.HexColor('#34495e'),
        fontName='Helvetica-Bold'
    )

    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.HexColor('#7f8c8d'),
        fontName='Helvetica-Bold'
    )

    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=6,
        alignment=TA_JUSTIFY,
        fontName='Helvetica'
    )

    code_style = ParagraphStyle(
        'CodeStyle',
        parent=styles['Normal'],
        fontSize=9,
        spaceAfter=6,
        fontName='Courier',
        backColor=colors.HexColor('#f8f9fa'),
        borderColor=colors.HexColor('#e9ecef'),
        borderWidth=1,
        borderPadding=10
    )

    info_box_style = ParagraphStyle(
        'InfoBox',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=6,
        backColor=colors.HexColor('#ecf0f1'),
        borderColor=colors.HexColor('#3498db'),
        borderWidth=1,
        borderPadding=15,
        leftIndent=20
    )

    story = []

    # Página de título
    story.append(Spacer(1, 3*cm))
    story.append(Paragraph("TRABALHO PRÁTICO", title_style))
    story.append(Paragraph("DESENVOLVIMENTO MOBILE", title_style))
    story.append(Paragraph("APLICATIVO HAMBURGUERIAZ", title_style))
    story.append(Spacer(1, 2*cm))

    # Informações do trabalho em caixa destacada
    info_text = """
    <b>Disciplina:</b> Desenvolvimento Mobile<br/>
    <b>Objetivo:</b> Desenvolver aplicativo Android completo seguindo roteiro de aula prática<br/>
    <b>Linguagem:</b> Java<br/>
    <b>IDE:</b> Android Studio Flamingo 2022.2.1<br/>
    <b>SDK Target:</b> API 34 (Android 14)<br/>
    <b>SDK Mínimo:</b> API 23 (Android 6.0)<br/>
    <b>Data:</b> 2024
    """
    story.append(Paragraph(info_text, info_box_style))
    story.append(Spacer(1, 2*cm))

    # Sumário
    story.append(Paragraph("SUMÁRIO", heading_style))
    sumario_text = """
    1. Introdução ............................................................. 3<br/>
    2. Objetivos .............................................................. 3<br/>
    3. Infraestrutura Utilizada ............................................... 4<br/>
    4. Desenvolvimento do Projeto ............................................. 5<br/>
    5. Implementação das Funcionalidades ...................................... 8<br/>
    6. Código Fonte Completo .................................................. 10<br/>
    7. Resultados Obtidos ..................................................... 15<br/>
    8. Conclusão .............................................................. 16<br/>
    9. Anexos - Screenshots e Estrutura ...................................... 17
    """
    story.append(Paragraph(sumario_text, normal_style))
    story.append(PageBreak())

    # 1. Introdução
    story.append(Paragraph("1. INTRODUÇÃO", heading_style))
    intro_text = """
    Este trabalho apresenta o desenvolvimento completo de um aplicativo Android para uma hamburgueria
    fictícia chamada "HamburgueriaZ". O projeto foi desenvolvido seguindo um roteiro estruturado de
    aula prática, implementando desde a interface básica até funcionalidades avançadas como integração
    com aplicativos externos através de Intents.
    <br/><br/>
    O aplicativo permite que clientes realizem pedidos de hambúrgueres diretamente pelo smartphone,
    escolhendo adicionais, quantidade e enviando o pedido por e-mail, eliminando a necessidade de
    aplicativos de terceiros. O desenvolvimento seguiu as melhores práticas de programação Android
    e design de interface, resultando em uma aplicação funcional e intuitiva.
    <br/><br/>
    O projeto demonstra a aplicação prática de conceitos fundamentais do desenvolvimento mobile,
    incluindo criação de layouts responsivos, implementação de lógica de negócio, manipulação de
    eventos de interface e integração com o sistema operacional Android através de Intents.
    """
    story.append(Paragraph(intro_text, normal_style))
    story.append(Spacer(1, 15))

    # 2. Objetivos
    story.append(Paragraph("2. OBJETIVOS", heading_style))

    story.append(Paragraph("2.1 Objetivos Gerais", subheading_style))
    obj_gerais = """
    • Desenvolver competências em desenvolvimento mobile para Android<br/>
    • Aplicar conceitos de interface de usuário (UI) e experiência do usuário (UX)<br/>
    • Implementar funcionalidades em Java para Android<br/>
    • Utilizar Intents para integração com aplicativos externos<br/>
    • Compreender o ciclo de desenvolvimento de aplicações móveis
    """
    story.append(Paragraph(obj_gerais, normal_style))

    story.append(Paragraph("2.2 Objetivos Específicos", subheading_style))
    obj_especificos = """
    • Dominar o uso do Android Studio como ambiente de desenvolvimento<br/>
    • Construir interface de aplicação Android com estilos predefinidos e imagens<br/>
    • Implementar funcionalidades de um app na linguagem Java<br/>
    • Utilizar Intents para interação com aplicativos externos<br/>
    • Aplicar validações e tratamento de erros<br/>
    • Exportar e documentar projeto do Android Studio<br/>
    • Realizar testes funcionais da aplicação
    """
    story.append(Paragraph(obj_especificos, normal_style))
    story.append(PageBreak())

    # 3. Infraestrutura
    story.append(Paragraph("3. INFRAESTRUTURA UTILIZADA", heading_style))

    story.append(Paragraph("3.1 Software Principal", subheading_style))
    software_text = """
    <b>Android Studio Flamingo | 2022.2.1</b><br/>
    • Ambiente de desenvolvimento integrado (IDE) oficial para desenvolvimento Android<br/>
    • Baseado no IntelliJ IDEA da JetBrains<br/>
    • Ferramentas avançadas de desenvolvimento e debugging<br/>
    • Editor de código com recursos de autocomplete e refatoração<br/>
    • Emulador integrado para testes<br/>
    • Sistema de build baseado em Gradle<br/>
    • Suporte completo para desenvolvimento em Java e Kotlin
    """
    story.append(Paragraph(software_text, info_box_style))

    story.append(Paragraph("3.2 Pré-requisitos e Dependências", subheading_style))
    prereq_text = """
    <b>Java Development Kit (JDK) 20</b><br/>
    • Ambiente de desenvolvimento para construção de aplicativos Java<br/>
    • Necessário para compilação do código Android<br/>
    • Inclui ferramentas de desenvolvimento e bibliotecas padrão<br/><br/>
    <b>Android SDK</b><br/>
    • SDK de Compilação: API 34 (Android 14)<br/>
    • SDK Mínimo: API 23 (Android 6.0)<br/>
    • Build Tools: 34.0.0<br/>
    • Gradle: 8.0
    """
    story.append(Paragraph(prereq_text, info_box_style))

    story.append(Paragraph("3.3 Configurações do Projeto", subheading_style))
    config_text = """
    <b>Configurações Técnicas:</b><br/>
    • <b>Package Name:</b> com.example.hamburgueriaz<br/>
    • <b>Activity Principal:</b> MainActivity<br/>
    • <b>Layout Principal:</b> activity_main.xml<br/>
    • <b>Linguagem:</b> Java<br/>
    • <b>Tipo de Licença:</b> Freeware<br/>
    • <b>Orientação:</b> Portrait (Retrato)<br/>
    • <b>Tema:</b> Material Design
    """
    story.append(Paragraph(config_text, normal_style))
    story.append(PageBreak())

    # 4. Desenvolvimento
    story.append(Paragraph("4. DESENVOLVIMENTO DO PROJETO", heading_style))

    story.append(Paragraph("ETAPA 1: CRIAÇÃO DO PROJETO", subheading_style))
    etapa1_text = """
    O projeto foi iniciado no Android Studio seguindo uma metodologia estruturada:<br/><br/>
    <b>1. Configuração Inicial do Projeto</b><br/>
    • File → New → New Project<br/>
    • Template: Empty Activity<br/>
    • Nome: HamburgueriaZ<br/>
    • Package name: com.example.hamburgueriaz<br/>
    • Linguagem: Java<br/>
    • SDK Mínimo: API 23 (Android 6.0)<br/>
    • SDK de Compilação: API 34 (Android 14)<br/><br/>
    <b>2. Estrutura Inicial Gerada</b><br/>
    • MainActivity.java - Classe principal da aplicação<br/>
    • activity_main.xml - Layout da tela principal<br/>
    • AndroidManifest.xml - Configurações da aplicação<br/>
    • build.gradle - Configurações de build e dependências
    """
    story.append(Paragraph(etapa1_text, normal_style))

    story.append(Paragraph("ETAPA 2: DESENVOLVIMENTO DA INTERFACE", subheading_style))
    interface_text = """
    A interface foi desenvolvida seguindo princípios de Material Design e usabilidade:<br/><br/>
    <b>Componentes da Interface Implementados:</b><br/><br/>
    <b>1. Logo e Branding</b><br/>
    • ImageView com logo vetorial da HamburgueriaZ<br/>
    • Posicionamento no topo como banner<br/>
    • Arquivo: logo_hamburgueria.xml (Vector Drawable)<br/><br/>
    <b>2. Campo de Identificação</b><br/>
    • EditText para nome do cliente<br/>
    • Hint: "Digite seu nome"<br/>
    • Validação obrigatória implementada<br/>
    • InputType: textPersonName<br/><br/>
    <b>3. Seleção de Adicionais</b><br/>
    • CheckBox para Bacon (+R$ 2,00)<br/>
    • CheckBox para Queijo (+R$ 2,00)<br/>
    • CheckBox para Onion Rings (+R$ 3,00)<br/>
    • Listeners para atualização automática do preço<br/><br/>
    <b>4. Controle de Quantidade</b><br/>
    • Botão "-" (subtrair) com proteção contra valores negativos<br/>
    • TextView central exibindo quantidade atual<br/>
    • Botão "+" (somar) sem limite superior<br/>
    • Layout horizontal centralizado<br/><br/>
    <b>5. Área de Informações</b><br/>
    • TextView para resumo do pedido<br/>
    • TextView para preço total com formatação monetária<br/>
    • Atualização em tempo real conforme seleções<br/><br/>
    <b>6. Ação Principal</b><br/>
    • Botão "ENVIAR PEDIDO" com destaque visual<br/>
    • Cor de fundo: #FF5722 (Material Orange)<br/>
    • Integração com Intent de e-mail
    """
    story.append(Paragraph(interface_text, normal_style))

    story.append(Paragraph("ETAPA 3: IMPLEMENTAÇÃO DE ESTILOS", subheading_style))
    estilos_text = """
    Foi criado um sistema de estilos personalizado para padronizar a aparência:<br/><br/>
    <b>Estilo "EstiloTexto" Implementado:</b><br/>
    • layout_width e layout_height: wrap_content<br/>
    • gravity: center_vertical<br/>
    • textAllCaps: true<br/>
    • textSize: 15sp<br/>
    • paddingTop e paddingBottom: 16dp<br/>
    • textStyle: bold<br/>
    • textColor: #333333<br/><br/>
    <b>Aplicação dos Estilos:</b><br/>
    • Títulos de seções utilizando EstiloTexto<br/>
    • Consistência visual em toda a aplicação<br/>
    • Hierarquia visual clara e organizada
    """
    story.append(Paragraph(estilos_text, info_box_style))
    story.append(PageBreak())

    # 5. Implementação das Funcionalidades
    story.append(Paragraph("5. IMPLEMENTAÇÃO DAS FUNCIONALIDADES", heading_style))

    story.append(Paragraph("5.1 Funcionalidades de Quantidade (Etapa 5)", subheading_style))
    quantidade_text = """
    <b>Métodos Implementados:</b><br/><br/>
    <b>• somar():</b> Incrementa quantidade e atualiza preço total<br/>
    <b>• subtrair():</b> Decrementa quantidade (mínimo 1) e atualiza preço total<br/><br/>
    <b>Características Técnicas:</b><br/>
    • Proteção contra valores negativos com validação<br/>
    • Atualização automática do preço total<br/>
    • Feedback visual imediato para o usuário<br/>
    • Toast de aviso quando tenta diminuir abaixo de 1<br/>
    • Sincronização entre interface e lógica de negócio
    """
    story.append(Paragraph(quantidade_text, info_box_style))

    story.append(Paragraph("5.2 Função Enviar Pedido (Etapa 6)", subheading_style))
    pedido_text = """
    <b>Funcionalidades Implementadas:</b><br/><br/>
    <b>1. Identificação e Validação do Cliente</b><br/>
    • Captura do nome digitado no EditText<br/>
    • Validação de campo obrigatório com trim()<br/>
    • Exibição de Toast em caso de campo vazio<br/><br/>
    <b>2. Verificação de Adicionais Selecionados</b><br/>
    • Verificação do estado de cada CheckBox<br/>
    • Identificação dos itens selecionados (isChecked())<br/>
    • Mapeamento para variáveis booleanas<br/><br/>
    <b>3. Cálculo Dinâmico do Preço Total</b><br/>
    • Preço base do hambúrguer: R$ 20,00<br/>
    • Adicional Bacon: +R$ 2,00<br/>
    • Adicional Queijo: +R$ 2,00<br/>
    • Adicional Onion Rings: +R$ 3,00<br/>
    • Multiplicação pela quantidade selecionada<br/>
    • Formatação monetária brasileira<br/><br/>
    <b>4. Geração do Resumo Detalhado</b><br/>
    • Nome do cliente<br/>
    • Status de cada adicional (Sim/Não)<br/>
    • Quantidade de hambúrgueres<br/>
    • Preço final formatado em reais<br/>
    • Exibição na interface antes do envio
    """
    story.append(Paragraph(pedido_text, normal_style))

    story.append(Paragraph("5.3 Implementação de Intents (Etapa 7)", subheading_style))
    intent_text = """
    <b>Intent para E-mail com as seguintes características:</b><br/><br/>
    <b>Configuração Técnica:</b><br/>
    • Tipo: Intent.ACTION_SENDTO<br/>
    • Protocolo: mailto: (específico para e-mail)<br/>
    • Verificação de disponibilidade com resolveActivity()<br/>
    • Tratamento de erro quando não há app de e-mail<br/><br/>
    <b>Conteúdo do E-mail:</b><br/>
    • Assunto automático: "Pedido de [nome do cliente]"<br/>
    • Corpo: Resumo completo do pedido formatado<br/>
    • Dados estruturados e legíveis<br/><br/>
    <b>Funcionalidade:</b><br/>
    Ao clicar em "ENVIAR PEDIDO", o aplicativo abre automaticamente o aplicativo
    de e-mail padrão do dispositivo com o assunto e corpo já preenchidos com as
    informações do pedido, permitindo ao cliente enviar diretamente para a hamburgueria.
    """
    story.append(Paragraph(intent_text, info_box_style))
    story.append(PageBreak())

    # 6. Código Fonte Completo
    story.append(Paragraph("6. CÓDIGO FONTE COMPLETO", heading_style))

    story.append(Paragraph("6.1 MainActivity.java", subheading_style))

    # Código MainActivity - Parte 1
    codigo_main_1 = """package com.example.hamburgueriaz;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

public class MainActivity extends AppCompatActivity {

    // Declaração das variáveis dos componentes da interface
    private EditText editTextNome;
    private CheckBox checkBoxBacon, checkBoxQueijo, checkBoxOnionRings;
    private TextView textViewQuantidadeValor, textViewResumo, textViewPrecoTotal;
    private Button buttonSomar, buttonSubtrair, buttonEnviarPedido;

    // Variável para controlar a quantidade
    private int quantidade = 1;

    // Preços dos itens
    private final double PRECO_BASE = 20.0;
    private final double PRECO_BACON = 2.0;
    private final double PRECO_QUEIJO = 2.0;
    private final double PRECO_ONION_RINGS = 3.0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Inicialização dos componentes da interface
        inicializarComponentes();

        // Configuração dos listeners dos botões
        configurarListeners();

        // Atualizar o preço inicial
        atualizarPrecoTotal();
    }"""

    story.append(Paragraph(codigo_main_1, code_style))
    story.append(Spacer(1, 10))

    # Código MainActivity - Parte 2
    codigo_main_2 = """    /**
     * Método para inicializar todos os componentes da interface
     */
    private void inicializarComponentes() {
        editTextNome = findViewById(R.id.editTextNome);
        checkBoxBacon = findViewById(R.id.checkBoxBacon);
        checkBoxQueijo = findViewById(R.id.checkBoxQueijo);
        checkBoxOnionRings = findViewById(R.id.checkBoxOnionRings);
        textViewQuantidadeValor = findViewById(R.id.textViewQuantidadeValor);
        textViewResumo = findViewById(R.id.textViewResumo);
        textViewPrecoTotal = findViewById(R.id.textViewPrecoTotal);
        buttonSomar = findViewById(R.id.buttonSomar);
        buttonSubtrair = findViewById(R.id.buttonSubtrair);
        buttonEnviarPedido = findViewById(R.id.buttonEnviarPedido);
    }

    /**
     * Método para configurar os listeners dos botões e checkboxes
     */
    private void configurarListeners() {
        // Listener para o botão somar
        buttonSomar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                somar();
            }
        });

        // Listener para o botão subtrair
        buttonSubtrair.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                subtrair();
            }
        });

        // Listener para o botão enviar pedido
        buttonEnviarPedido.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                enviarPedido();
            }
        });

        // Listeners para os checkboxes para atualizar o preço automaticamente
        View.OnClickListener checkboxListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                atualizarPrecoTotal();
            }
        };

        checkBoxBacon.setOnClickListener(checkboxListener);
        checkBoxQueijo.setOnClickListener(checkboxListener);
        checkBoxOnionRings.setOnClickListener(checkboxListener);
    }"""

    story.append(Paragraph(codigo_main_2, code_style))
    story.append(PageBreak())

    # Continuação do código MainActivity - Parte 3
    codigo_main_3 = """    /**
     * Método para somar quantidade (Etapa 5)
     */
    public void somar() {
        quantidade++;
        textViewQuantidadeValor.setText(String.valueOf(quantidade));
        atualizarPrecoTotal();
    }

    /**
     * Método para subtrair quantidade (Etapa 5)
     * Atenção: Não permite quantidades negativas!
     */
    public void subtrair() {
        if (quantidade > 1) {
            quantidade--;
            textViewQuantidadeValor.setText(String.valueOf(quantidade));
            atualizarPrecoTotal();
        } else {
            Toast.makeText(this, "Quantidade mínima é 1!", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Método para calcular e atualizar o preço total
     */
    private void atualizarPrecoTotal() {
        double precoTotal = calcularPrecoTotal();
        textViewPrecoTotal.setText(String.format("PREÇO TOTAL: R$ %.2f", precoTotal));
    }

    /**
     * Método para calcular o preço total do pedido (Etapa 6)
     */
    private double calcularPrecoTotal() {
        double precoUnitario = PRECO_BASE;

        // Adicionar preços dos adicionais selecionados
        if (checkBoxBacon.isChecked()) {
            precoUnitario += PRECO_BACON;
        }
        if (checkBoxQueijo.isChecked()) {
            precoUnitario += PRECO_QUEIJO;
        }
        if (checkBoxOnionRings.isChecked()) {
            precoUnitario += PRECO_ONION_RINGS;
        }

        // Multiplicar pela quantidade
        return precoUnitario * quantidade;
    }"""

    story.append(Paragraph(codigo_main_3, code_style))
    story.append(Spacer(1, 10))

    # Código MainActivity - Parte 4 (Final)
    codigo_main_4 = """    /**
     * Método principal para enviar o pedido (Etapa 6 e 7)
     */
    public void enviarPedido() {
        // Identificar o nome do usuário
        String nomeCliente = editTextNome.getText().toString().trim();

        // Validar se o nome foi preenchido
        if (nomeCliente.isEmpty()) {
            Toast.makeText(this, "Por favor, digite seu nome!", Toast.LENGTH_SHORT).show();
            return;
        }

        // Identificar quais adicionais foram selecionados
        boolean temBacon = checkBoxBacon.isChecked();
        boolean temQueijo = checkBoxQueijo.isChecked();
        boolean temOnionRings = checkBoxOnionRings.isChecked();

        // Calcular o preço total
        double precoTotal = calcularPrecoTotal();

        // Criar a mensagem do resumo do pedido
        String resumoPedido = criarResumoPedido(nomeCliente, temBacon, temQueijo, temOnionRings, precoTotal);

        // Exibir o resumo na tela
        textViewResumo.setText(resumoPedido);

        // Criar e enviar o Intent para e-mail (Etapa 7)
        enviarPorEmail(nomeCliente, resumoPedido);
    }

    /**
     * Método para criar a mensagem do resumo do pedido (Etapa 6)
     */
    private String criarResumoPedido(String nome, boolean bacon, boolean queijo, boolean onionRings, double preco) {
        StringBuilder resumo = new StringBuilder();
        resumo.append("Nome do cliente: ").append(nome).append("\\n");
        resumo.append("Tem Bacon? ").append(bacon ? "Sim" : "Não").append("\\n");
        resumo.append("Tem Queijo? ").append(queijo ? "Sim" : "Não").append("\\n");
        resumo.append("Tem Onion Rings? ").append(onionRings ? "Sim" : "Não").append("\\n");
        resumo.append("Quantidade: ").append(quantidade).append("\\n");
        resumo.append("Preço final: R$ ").append(String.format("%.2f", preco));

        return resumo.toString();
    }

    /**
     * Método para enviar pedido por e-mail usando Intent (Etapa 7)
     */
    private void enviarPorEmail(String nomeCliente, String resumoPedido) {
        // Criar Intent do tipo ACTION_SENDTO para e-mail
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);

        // Configurar para abrir apenas aplicativos de e-mail
        emailIntent.setData(Uri.parse("mailto:"));

        // Definir o assunto do e-mail
        String assunto = "Pedido de " + nomeCliente;
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, assunto);

        // Definir o corpo do e-mail com o resumo do pedido
        emailIntent.putExtra(Intent.EXTRA_TEXT, resumoPedido);

        // Verificar se existe um aplicativo de e-mail disponível
        if (emailIntent.resolveActivity(getPackageManager()) != null) {
            startActivity(emailIntent);
        } else {
            Toast.makeText(this, "Nenhum aplicativo de e-mail encontrado!", Toast.LENGTH_LONG).show();
        }
    }
}"""

    story.append(Paragraph(codigo_main_4, code_style))
    story.append(PageBreak())

    # 7. Resultados
    story.append(Paragraph("7. RESULTADOS OBTIDOS", heading_style))

    story.append(Paragraph("7.1 Funcionalidades Implementadas com Sucesso", subheading_style))
    resultados_text = """
    <b>✅ Interface Completa e Responsiva</b><br/>
    • Layout adaptável a diferentes tamanhos de tela<br/>
    • Componentes organizados hierarquicamente<br/>
    • Design intuitivo seguindo Material Design<br/><br/>
    <b>✅ Lógica de Negócio Robusta</b><br/>
    • Cálculo correto de preços com validações<br/>
    • Controle de quantidade com proteções<br/>
    • Atualização em tempo real da interface<br/><br/>
    <b>✅ Integração Externa Funcional</b><br/>
    • Intent para e-mail funcionando corretamente<br/>
    • Tratamento de casos de erro<br/>
    • Verificação de disponibilidade de aplicativos<br/><br/>
    <b>✅ Experiência do Usuário Otimizada</b><br/>
    • Feedback visual adequado<br/>
    • Mensagens de erro informativas<br/>
    • Fluxo de uso intuitivo e direto
    """
    story.append(Paragraph(resultados_text, normal_style))

    story.append(Paragraph("7.2 Testes Realizados", subheading_style))
    testes_text = """
    <b>1. Testes de Interface</b><br/>
    • Todos os componentes respondem adequadamente aos toques<br/>
    • Layouts se adaptam corretamente a diferentes orientações<br/>
    • Textos e imagens são exibidos corretamente<br/><br/>
    <b>2. Testes de Funcionalidades</b><br/>
    • Cálculo de preços validado com diferentes combinações<br/>
    • Controle de quantidade testado em cenários extremos<br/>
    • Validações de entrada funcionando corretamente<br/><br/>
    <b>3. Testes de Integração</b><br/>
    • Intent abre aplicativo de e-mail corretamente<br/>
    • Dados são transferidos adequadamente<br/>
    • Tratamento de erro quando não há app de e-mail disponível
    """
    story.append(Paragraph(testes_text, info_box_style))
    story.append(PageBreak())

    # 8. Conclusão
    story.append(Paragraph("8. CONCLUSÃO", heading_style))
    conclusao_text = """
    O desenvolvimento do aplicativo HamburgueriaZ foi concluído com êxito, atendendo integralmente
    a todos os objetivos propostos no roteiro de aula prática. O projeto demonstrou de forma
    prática a aplicação de conceitos fundamentais do desenvolvimento Android, desde a criação
    de interfaces até a implementação de funcionalidades avançadas.
    <br/><br/>
    <b>Principais Conquistas Técnicas:</b><br/><br/>
    <b>1. Domínio do Ambiente de Desenvolvimento</b><br/>
    • Proficiência no uso do Android Studio<br/>
    • Compreensão da estrutura de projetos Android<br/>
    • Utilização eficiente das ferramentas de desenvolvimento<br/><br/>
    <b>2. Implementação de Interface Profissional</b><br/>
    • Criação de layouts responsivos e intuitivos<br/>
    • Aplicação de estilos personalizados consistentes<br/>
    • Uso adequado de componentes Android nativos<br/><br/>
    <b>3. Programação Orientada a Objetos em Java</b><br/>
    • Implementação de lógica de negócio robusta<br/>
    • Manipulação eficiente de eventos de interface<br/>
    • Gerenciamento adequado do estado da aplicação<br/><br/>
    <b>4. Integração com Ecossistema Android</b><br/>
    • Uso correto de Intents para comunicação entre apps<br/>
    • Tratamento apropriado de casos de erro<br/>
    • Validação de disponibilidade de recursos do sistema<br/><br/>
    <b>5. Aplicação de Boas Práticas</b><br/>
    • Código bem estruturado e documentado<br/>
    • Separação clara de responsabilidades<br/>
    • Tratamento adequado de exceções e validações
    """
    story.append(Paragraph(conclusao_text, normal_style))

    story.append(Paragraph("Impacto Educacional e Profissional", subheading_style))
    impacto_text = """
    Este projeto proporcionou uma experiência completa de desenvolvimento mobile, desde a
    concepção até a implementação final. As competências desenvolvidas incluem não apenas
    aspectos técnicos, mas também metodológicos, preparando para desafios reais do
    desenvolvimento de aplicações móveis.
    <br/><br/>
    O aplicativo HamburgueriaZ representa uma solução funcional e escalável, demonstrando
    como conceitos teóricos de desenvolvimento mobile podem ser aplicados na prática para
    resolver problemas reais de negócio, estabelecendo uma base sólida para projetos
    futuros mais complexos.
    """
    story.append(Paragraph(impacto_text, info_box_style))

    # Finalizar e gerar o PDF
    doc.build(story)
    print("PDF completo criado com sucesso: Trabalho_Desenvolvimento_Mobile_HamburgueriaZ_COMPLETO.pdf")

def create_simple_pdf():
    """Cria PDF simples sem bibliotecas externas"""
    content = """TRABALHO PRÁTICO - DESENVOLVIMENTO MOBILE
APLICATIVO HAMBURGUERIAZ

Disciplina: Desenvolvimento Mobile
Objetivo: Desenvolver aplicativo Android completo seguindo roteiro de aula prática
Linguagem: Java
IDE: Android Studio
Data: 2024

SUMÁRIO
1. Introdução
2. Objetivos
3. Infraestrutura Utilizada
4. Desenvolvimento do Projeto
5. Implementação das Funcionalidades
6. Resultados Obtidos
7. Conclusão
8. Anexos - Código Fonte

1. INTRODUÇÃO
Este trabalho apresenta o desenvolvimento completo de um aplicativo Android para uma hamburgueria fictícia chamada "HamburgueriaZ". O projeto foi desenvolvido seguindo um roteiro estruturado de aula prática, implementando desde a interface básica até funcionalidades avançadas como integração com aplicativos externos através de Intents.

O aplicativo permite que clientes realizem pedidos de hambúrgueres diretamente pelo smartphone, escolhendo adicionais, quantidade e enviando o pedido por e-mail, eliminando a necessidade de aplicativos de terceiros.

2. OBJETIVOS

2.1 Objetivos Gerais
• Desenvolver competências em desenvolvimento mobile para Android
• Aplicar conceitos de interface de usuário (UI) e experiência do usuário (UX)
• Implementar funcionalidades em Java para Android
• Utilizar Intents para integração com aplicativos externos

2.2 Objetivos Específicos
• Saber utilizar o software Android Studio
• Construir interface de aplicação Android com estilos predefinidos e imagens
• Implementar funcionalidades de um app na linguagem Java
• Utilizar Intents para interação com aplicativos externos
• Exportar projeto do Android Studio

3. INFRAESTRUTURA UTILIZADA

3.1 Software Principal
Android Studio Flamingo | 2022.2.1
• Ambiente de desenvolvimento integrado (IDE) oficial para desenvolvimento Android
• Baseado no IntelliJ IDEA
• Ferramentas avançadas de desenvolvimento
• Editor de código com recursos de autocomplete e debugging

3.2 Pré-requisitos
Java JDK 20
• Ambiente de desenvolvimento para construção de aplicativos Java
• Necessário para compilação do código Android

3.3 Configurações do Projeto
• SDK Mínimo: API 23 (Android 6.0)
• SDK de Compilação: API 34 (Android 14)
• Linguagem: Java
• Tipo de Licença: Freeware

[Conteúdo continua...]
"""

    # Salvar como arquivo de texto que pode ser convertido
    with open("trabalho_temp.txt", "w", encoding="utf-8") as f:
        f.write(content)

    print("Arquivo de texto criado. Para converter para PDF, use uma ferramenta online ou software específico.")

if __name__ == "__main__":
    if reportlab_available:
        create_pdf_with_reportlab()
    else:
        print("ReportLab não está disponível. Tentando instalar...")
        import subprocess
        try:
            subprocess.check_call(["pip", "install", "reportlab"])
            print("ReportLab instalado. Execute o script novamente.")
        except:
            print("Não foi possível instalar ReportLab. Criando arquivo de texto...")
            create_simple_pdf()
