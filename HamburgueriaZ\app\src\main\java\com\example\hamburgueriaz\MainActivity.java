package com.example.hamburgueriaz;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

public class MainActivity extends AppCompatActivity {

    // Declaração das variáveis dos componentes da interface
    private EditText editTextNome;
    private CheckBox checkBoxBacon, checkBoxQueijo, checkBoxOnionRings;
    private TextView textViewQuantidadeValor, textViewResumo, textViewPrecoTotal;
    private Button buttonSomar, buttonSubtrair, buttonEnviarPedido;
    
    // Variável para controlar a quantidade
    private int quantidade = 1;
    
    // Preços dos itens
    private final double PRECO_BASE = 20.0;
    private final double PRECO_BACON = 2.0;
    private final double PRECO_QUEIJO = 2.0;
    private final double PRECO_ONION_RINGS = 3.0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // Inicialização dos componentes da interface
        inicializarComponentes();
        
        // Configuração dos listeners dos botões
        configurarListeners();
        
        // Atualizar o preço inicial
        atualizarPrecoTotal();
    }
    
    /**
     * Método para inicializar todos os componentes da interface
     */
    private void inicializarComponentes() {
        editTextNome = findViewById(R.id.editTextNome);
        checkBoxBacon = findViewById(R.id.checkBoxBacon);
        checkBoxQueijo = findViewById(R.id.checkBoxQueijo);
        checkBoxOnionRings = findViewById(R.id.checkBoxOnionRings);
        textViewQuantidadeValor = findViewById(R.id.textViewQuantidadeValor);
        textViewResumo = findViewById(R.id.textViewResumo);
        textViewPrecoTotal = findViewById(R.id.textViewPrecoTotal);
        buttonSomar = findViewById(R.id.buttonSomar);
        buttonSubtrair = findViewById(R.id.buttonSubtrair);
        buttonEnviarPedido = findViewById(R.id.buttonEnviarPedido);
    }
    
    /**
     * Método para configurar os listeners dos botões e checkboxes
     */
    private void configurarListeners() {
        // Listener para o botão somar
        buttonSomar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                somar();
            }
        });
        
        // Listener para o botão subtrair
        buttonSubtrair.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                subtrair();
            }
        });
        
        // Listener para o botão enviar pedido
        buttonEnviarPedido.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                enviarPedido();
            }
        });
        
        // Listeners para os checkboxes para atualizar o preço automaticamente
        View.OnClickListener checkboxListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                atualizarPrecoTotal();
            }
        };
        
        checkBoxBacon.setOnClickListener(checkboxListener);
        checkBoxQueijo.setOnClickListener(checkboxListener);
        checkBoxOnionRings.setOnClickListener(checkboxListener);
    }
    
    /**
     * Método para somar quantidade (Etapa 5)
     */
    public void somar() {
        quantidade++;
        textViewQuantidadeValor.setText(String.valueOf(quantidade));
        atualizarPrecoTotal();
    }
    
    /**
     * Método para subtrair quantidade (Etapa 5)
     * Atenção: Não permite quantidades negativas!
     */
    public void subtrair() {
        if (quantidade > 1) {
            quantidade--;
            textViewQuantidadeValor.setText(String.valueOf(quantidade));
            atualizarPrecoTotal();
        } else {
            Toast.makeText(this, "Quantidade mínima é 1!", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Método para calcular e atualizar o preço total
     */
    private void atualizarPrecoTotal() {
        double precoTotal = calcularPrecoTotal();
        textViewPrecoTotal.setText(String.format("PREÇO TOTAL: R$ %.2f", precoTotal));
    }
    
    /**
     * Método para calcular o preço total do pedido (Etapa 6)
     */
    private double calcularPrecoTotal() {
        double precoUnitario = PRECO_BASE;
        
        // Adicionar preços dos adicionais selecionados
        if (checkBoxBacon.isChecked()) {
            precoUnitario += PRECO_BACON;
        }
        if (checkBoxQueijo.isChecked()) {
            precoUnitario += PRECO_QUEIJO;
        }
        if (checkBoxOnionRings.isChecked()) {
            precoUnitario += PRECO_ONION_RINGS;
        }
        
        // Multiplicar pela quantidade
        return precoUnitario * quantidade;
    }
    
    /**
     * Método principal para enviar o pedido (Etapa 6 e 7)
     */
    public void enviarPedido() {
        // Identificar o nome do usuário
        String nomeCliente = editTextNome.getText().toString().trim();
        
        // Validar se o nome foi preenchido
        if (nomeCliente.isEmpty()) {
            Toast.makeText(this, "Por favor, digite seu nome!", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Identificar quais adicionais foram selecionados
        boolean temBacon = checkBoxBacon.isChecked();
        boolean temQueijo = checkBoxQueijo.isChecked();
        boolean temOnionRings = checkBoxOnionRings.isChecked();
        
        // Calcular o preço total
        double precoTotal = calcularPrecoTotal();
        
        // Criar a mensagem do resumo do pedido
        String resumoPedido = criarResumoPedido(nomeCliente, temBacon, temQueijo, temOnionRings, precoTotal);
        
        // Exibir o resumo na tela
        textViewResumo.setText(resumoPedido);
        
        // Criar e enviar o Intent para e-mail (Etapa 7)
        enviarPorEmail(nomeCliente, resumoPedido);
    }
    
    /**
     * Método para criar a mensagem do resumo do pedido (Etapa 6)
     */
    private String criarResumoPedido(String nome, boolean bacon, boolean queijo, boolean onionRings, double preco) {
        StringBuilder resumo = new StringBuilder();
        resumo.append("Nome do cliente: ").append(nome).append("\n");
        resumo.append("Tem Bacon? ").append(bacon ? "Sim" : "Não").append("\n");
        resumo.append("Tem Queijo? ").append(queijo ? "Sim" : "Não").append("\n");
        resumo.append("Tem Onion Rings? ").append(onionRings ? "Sim" : "Não").append("\n");
        resumo.append("Quantidade: ").append(quantidade).append("\n");
        resumo.append("Preço final: R$ ").append(String.format("%.2f", preco));
        
        return resumo.toString();
    }
    
    /**
     * Método para enviar pedido por e-mail usando Intent (Etapa 7)
     */
    private void enviarPorEmail(String nomeCliente, String resumoPedido) {
        // Criar Intent do tipo ACTION_SENDTO para e-mail
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
        
        // Configurar para abrir apenas aplicativos de e-mail
        emailIntent.setData(Uri.parse("mailto:"));
        
        // Definir o assunto do e-mail
        String assunto = "Pedido de " + nomeCliente;
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, assunto);
        
        // Definir o corpo do e-mail com o resumo do pedido
        emailIntent.putExtra(Intent.EXTRA_TEXT, resumoPedido);
        
        // Verificar se existe um aplicativo de e-mail disponível
        if (emailIntent.resolveActivity(getPackageManager()) != null) {
            startActivity(emailIntent);
        } else {
            Toast.makeText(this, "Nenhum aplicativo de e-mail encontrado!", Toast.LENGTH_LONG).show();
        }
    }
}
