# TRABALHO PRÁTICO - DESENVOLVIMENTO MOBILE
## APLICATIVO HAMBURGUERIAZ

---

**Disciplina:** Desenvolvimento Mobile  
**Objetivo:** Desenvolver aplicativo Android completo seguindo roteiro de aula prática  
**Linguagem:** Java  
**IDE:** Android Studio  
**Data:** 2024  

---

## SUMÁRIO

1. [Introdução](#introdução)
2. [Objetivos](#objetivos)
3. [Infraestrutura Utilizada](#infraestrutura-utilizada)
4. [Desenvolvimento do Projeto](#desenvolvimento-do-projeto)
5. [Implementação das Funcionalidades](#implementação-das-funcionalidades)
6. [Resultados Obtidos](#resultados-obtidos)
7. [Conclusão](#conclusão)
8. [Anexos - Código F<PERSON>](#anexos---código-fonte)

---

## INTRODUÇÃO

Este trabalho apresenta o desenvolvimento completo de um aplicativo Android para uma hamburgueria fictícia chamada "HamburgueriaZ". O projeto foi desenvolvido seguindo um roteiro estruturado de aula prática, implementando desde a interface básica até funcionalidades avançadas como integração com aplicativos externos através de Intents.

O aplicativo permite que clientes realizem pedidos de hambúrgueres diretamente pelo smartphone, escolhendo adicionais, quantidade e enviando o pedido por e-mail, eliminando a necessidade de aplicativos de terceiros.

## OBJETIVOS

### Objetivos Gerais
- Desenvolver competências em desenvolvimento mobile para Android
- Aplicar conceitos de interface de usuário (UI) e experiência do usuário (UX)
- Implementar funcionalidades em Java para Android
- Utilizar Intents para integração com aplicativos externos

### Objetivos Específicos
- Saber utilizar o software Android Studio
- Construir interface de aplicação Android com estilos predefinidos e imagens
- Implementar funcionalidades de um app na linguagem Java
- Utilizar Intents para interação com aplicativos externos
- Exportar projeto do Android Studio

## INFRAESTRUTURA UTILIZADA

### Software Principal
- **Android Studio Flamingo | 2022.2.1**
  - Ambiente de desenvolvimento integrado (IDE) oficial para desenvolvimento Android
  - Baseado no IntelliJ IDEA
  - Ferramentas avançadas de desenvolvimento
  - Editor de código com recursos de autocomplete e debugging

### Pré-requisitos
- **Java JDK 20**
  - Ambiente de desenvolvimento para construção de aplicativos Java
  - Necessário para compilação do código Android

### Configurações do Projeto
- **SDK Mínimo:** API 23 (Android 6.0)
- **SDK de Compilação:** API 34 (Android 14)
- **Linguagem:** Java
- **Tipo de Licença:** Freeware

## DESENVOLVIMENTO DO PROJETO

### ETAPA 1: CRIAÇÃO DO PROJETO

O projeto foi iniciado no Android Studio seguindo os seguintes passos:

1. **Criação do Novo Projeto**
   - File > New > New Project
   - Template: Empty Activity
   - Nome: HamburgueriaZ
   - Linguagem: Java
   - SDK Mínimo: API 23

2. **Estrutura Inicial Gerada**
   ```
   HamburgueriaZ/
   ├── app/
   │   ├── src/main/
   │   │   ├── java/
   │   │   ├── res/
   │   │   └── AndroidManifest.xml
   │   └── build.gradle
   ├── build.gradle
   └── settings.gradle
   ```

### ETAPA 2: DESENVOLVIMENTO DA INTERFACE INICIAL

A interface foi desenvolvida com foco na usabilidade e experiência do usuário, implementando todos os requisitos especificados:

#### Componentes Implementados:

1. **Campo Nome do Cliente**
   - EditText para entrada de texto
   - Validação obrigatória
   - Hint explicativo

2. **Seleção de Adicionais**
   - CheckBox para Bacon (+R$ 2,00)
   - CheckBox para Queijo (+R$ 2,00)
   - CheckBox para Onion Rings (+R$ 3,00)
   - Atualização automática do preço

3. **Controle de Quantidade**
   - Botão "-" para diminuir
   - TextView central mostrando quantidade
   - Botão "+" para aumentar
   - Proteção contra valores negativos

4. **Exibição de Informações**
   - TextView para preço total
   - TextView para resumo do pedido
   - Atualização em tempo real

5. **Ação Principal**
   - Botão "Enviar Pedido"
   - Integração com Intent de e-mail

### ETAPA 3: PADRONIZAÇÃO DE ESTILOS

Foi criado um estilo personalizado para padronizar a aparência dos textos:

#### Estilo "EstiloTexto" Implementado:
```xml
<style name="EstiloTexto">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">15sp</item>
    <item name="android:paddingTop">16dp</item>
    <item name="android:paddingBottom">16dp</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textColor">#333333</item>
</style>
```

#### Aplicação do Estilo:
- TextView "Faça seu pedido"
- TextView "Quantidade"
- TextView "Resumo do pedido"
- TextView do valor total

### ETAPA 4: ADIÇÃO DE IMAGENS

Foi implementada uma ImageView no topo da aplicação funcionando como banner:

#### Características da Imagem:
- Logo vetorial personalizada da HamburgueriaZ
- Posicionamento no topo da tela
- Estilo de banner responsivo
- Cores harmoniosas com o tema do app

## IMPLEMENTAÇÃO DAS FUNCIONALIDADES

### ETAPA 5: FUNCIONALIDADES DE QUANTIDADE

#### Método somar():
```java
public void somar() {
    quantidade++;
    textViewQuantidadeValor.setText(String.valueOf(quantidade));
    atualizarPrecoTotal();
}
```

#### Método subtrair():
```java
public void subtrair() {
    if (quantidade > 1) {
        quantidade--;
        textViewQuantidadeValor.setText(String.valueOf(quantidade));
        atualizarPrecoTotal();
    } else {
        Toast.makeText(this, "Quantidade mínima é 1!", Toast.LENGTH_SHORT).show();
    }
}
```

**Características Implementadas:**
- Incremento e decremento da quantidade
- Proteção contra valores negativos
- Atualização automática do preço
- Feedback visual para o usuário

### ETAPA 6: FUNÇÃO ENVIAR PEDIDO

#### Funcionalidades Implementadas:

1. **Identificação do Cliente**
   - Captura do nome digitado
   - Validação de campo obrigatório

2. **Verificação de Adicionais**
   - Verificação do estado dos CheckBoxes
   - Identificação dos itens selecionados

3. **Cálculo do Preço Total**
   ```java
   private double calcularPrecoTotal() {
       double precoUnitario = PRECO_BASE;
       
       if (checkBoxBacon.isChecked()) {
           precoUnitario += PRECO_BACON;
       }
       if (checkBoxQueijo.isChecked()) {
           precoUnitario += PRECO_QUEIJO;
       }
       if (checkBoxOnionRings.isChecked()) {
           precoUnitario += PRECO_ONION_RINGS;
       }
       
       return precoUnitario * quantidade;
   }
   ```

4. **Geração do Resumo**
   ```java
   private String criarResumoPedido(String nome, boolean bacon, boolean queijo, boolean onionRings, double preco) {
       StringBuilder resumo = new StringBuilder();
       resumo.append("Nome do cliente: ").append(nome).append("\n");
       resumo.append("Tem Bacon? ").append(bacon ? "Sim" : "Não").append("\n");
       resumo.append("Tem Queijo? ").append(queijo ? "Sim" : "Não").append("\n");
       resumo.append("Tem Onion Rings? ").append(onionRings ? "Sim" : "Não").append("\n");
       resumo.append("Quantidade: ").append(quantidade).append("\n");
       resumo.append("Preço final: R$ ").append(String.format("%.2f", preco));
       
       return resumo.toString();
   }
   ```

### ETAPA 7: IMPLEMENTAÇÃO DE INTENTS

#### Intent para E-mail:
```java
private void enviarPorEmail(String nomeCliente, String resumoPedido) {
    Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
    emailIntent.setData(Uri.parse("mailto:"));
    
    String assunto = "Pedido de " + nomeCliente;
    emailIntent.putExtra(Intent.EXTRA_SUBJECT, assunto);
    emailIntent.putExtra(Intent.EXTRA_TEXT, resumoPedido);
    
    if (emailIntent.resolveActivity(getPackageManager()) != null) {
        startActivity(emailIntent);
    } else {
        Toast.makeText(this, "Nenhum aplicativo de e-mail encontrado!", Toast.LENGTH_LONG).show();
    }
}
```

#### Características do Intent:
- Tipo: ACTION_SENDTO
- Protocolo: mailto:
- Assunto automático: "Pedido de [nome do cliente]"
- Corpo: Resumo completo do pedido
- Verificação de disponibilidade de apps de e-mail

## RESULTADOS OBTIDOS

### Funcionalidades Implementadas com Sucesso:

✅ **Interface Completa**
- Layout responsivo e intuitivo
- Componentes funcionais
- Design atrativo

✅ **Lógica de Negócio**
- Cálculo correto de preços
- Validações adequadas
- Controle de estado

✅ **Integração Externa**
- Intent funcionando corretamente
- Abertura de aplicativo de e-mail
- Dados transferidos adequadamente

✅ **Experiência do Usuário**
- Feedback visual adequado
- Prevenção de erros
- Fluxo intuitivo

### Testes Realizados:

1. **Teste de Interface**
   - Todos os componentes respondem adequadamente
   - Layout se adapta a diferentes tamanhos de tela

2. **Teste de Funcionalidades**
   - Cálculo de preços correto
   - Controle de quantidade funcionando
   - Validações operando adequadamente

3. **Teste de Integração**
   - Intent abre aplicativo de e-mail
   - Dados são transferidos corretamente
   - Tratamento de erro quando não há app de e-mail

### Estrutura Final do Projeto:

```
HamburgueriaZ/
├── app/
│   ├── src/main/
│   │   ├── java/com/example/hamburgueriaz/
│   │   │   └── MainActivity.java (200+ linhas)
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml
│   │   │   ├── values/
│   │   │   │   ├── colors.xml
│   │   │   │   ├── strings.xml
│   │   │   │   └── themes.xml
│   │   │   └── drawable/
│   │   │       └── logo_hamburgueria.xml
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── build.gradle
├── settings.gradle
├── gradle.properties
└── README.md
```

## CONCLUSÃO

O desenvolvimento do aplicativo HamburgueriaZ foi concluído com sucesso, atendendo a todos os objetivos propostos no roteiro de aula prática. O projeto demonstrou a aplicação prática de conceitos fundamentais do desenvolvimento Android:

### Competências Desenvolvidas:

1. **Desenvolvimento de Interface**
   - Criação de layouts responsivos
   - Aplicação de estilos personalizados
   - Uso adequado de componentes Android

2. **Programação em Java para Android**
   - Implementação de lógica de negócio
   - Manipulação de eventos
   - Gerenciamento de estado da aplicação

3. **Integração com Sistema**
   - Uso de Intents para comunicação entre apps
   - Tratamento de casos de erro
   - Validação de disponibilidade de recursos

4. **Boas Práticas**
   - Código bem estruturado e comentado
   - Separação de responsabilidades
   - Tratamento adequado de exceções

### Resultados Alcançados:

- ✅ Aplicativo funcional e completo
- ✅ Interface intuitiva e atrativa
- ✅ Todas as funcionalidades implementadas
- ✅ Integração externa funcionando
- ✅ Código bem documentado
- ✅ Projeto exportável

O aplicativo HamburgueriaZ representa uma solução completa para pedidos de hamburgueria, demonstrando como conceitos teóricos de desenvolvimento mobile podem ser aplicados na prática para resolver problemas reais de negócio.

---

**Trabalho desenvolvido seguindo roteiro de aula prática de Desenvolvimento Mobile**  
**Todas as etapas foram implementadas com sucesso**  
**Projeto pronto para uso e expansão futura**
