# PORTFÓLIO - DESENVOLVIMENTO MOBILE
## Aplicativo HamburgueriaZ

**Disciplina:** Desenvolvimento Mobile  
**Aluno:** Renan  
**Data:** Dezembro 2024  

---

## SUMÁRIO

1. [Introdução](#introdução)
2. [Objetivos](#objetivos)
3. [Infraestrutura Utilizada](#infraestrutura-utilizada)
4. [Desenvolvimento do Projeto](#desenvolvimento-do-projeto)
5. [<PERSON><PERSON><PERSON>](#código-fonte)
6. [Resultados Obtidos](#resultados-obtidos)
7. [Conclus<PERSON>](#conclusão)

---

## INTRODUÇÃO

Este portfólio documenta o desenvolvimento completo do aplicativo **HamburgueriaZ**, uma aplicação Android desenvolvida durante a aula prática da disciplina de Desenvolvimento Mobile. O aplicativo permite que clientes de uma hamburgueria realizem pedidos diretamente pelo smartphone, sem necessidade de aplicativos de terceiros.

O projeto foi desenvolvido utilizando Android Studio e linguagem Java, seguindo as melhores práticas de desenvolvimento mobile e implementando funcionalidades como interface responsiva, cálculo de preços, e integração com aplicativos de e-mail através de Intents.

---

## OBJETIVOS

Os objetivos desta aula prática foram cumpridos integralmente:

✅ **Utilizar o software Android Studio** - Ambiente de desenvolvimento configurado e utilizado  
✅ **Construir interface Android** - Interface completa com estilos e imagens implementada  
✅ **Implementar funcionalidades em Java** - Lógica de negócio desenvolvida  
✅ **Utilizar Intents** - Integração com aplicativo de e-mail implementada  
✅ **Exportar projeto** - Projeto exportado em formato .zip  

---

## INFRAESTRUTURA UTILIZADA

### Software Utilizado:
- **Android Studio Flamingo | 2022.2.1**
  - Ambiente de desenvolvimento integrado (IDE) oficial para Android
  - Baseado no IntelliJ IDEA
  - Licença: Freeware

- **Java JDK 20**
  - Ambiente de desenvolvimento Java
  - Pré-requisito para Android Studio

### Configurações do Projeto:
- **Nome do Projeto:** HamburgueriaZ
- **Linguagem:** Java
- **SDK Mínimo:** API 23 (Android 6.0)
- **Tipo de Activity:** Empty Activity

---

## DESENVOLVIMENTO DO PROJETO

### Etapa 1: Criação do Projeto

O projeto foi iniciado através do Android Studio seguindo os passos:
1. **File > New > New Project**
2. Seleção de **Empty Activity**
3. Configuração do projeto com nome HamburgueriaZ, linguagem Java e SDK API 23

### Etapa 2: Interface Inicial

A interface foi desenvolvida contemplando todos os requisitos:
- Campo para inserir o nome do cliente
- Lista de checkboxes para adicionais (Bacon, Queijo, Onion Rings)
- Seleção de quantidade com botões + e -
- Exibição do preço total
- Botão para enviar pedido

### Etapa 3: Padronização de Estilos

Criado estilo personalizado "EstiloTexto" no arquivo themes.xml com:
- layout_width e layout_height: wrap_content
- gravity: center_vertical
- Texto em maiúsculas
- Tamanho: 15sp
- Padding superior e inferior: 16dp

### Etapa 4: Adição de Imagens

Implementada ImageView no topo da tela como banner da hamburgueria.

### Etapa 5: Funcionalidades de Quantidade

Implementadas funções "somar" e "subtrair" para os botões + e -, com validação para evitar quantidades negativas.

### Etapa 6: Função Enviar Pedido

Desenvolvida função que:
- Identifica nome do usuário
- Verifica adicionais selecionados
- Calcula valor total (hambúrguer R$ 20 + adicionais)
- Exibe resumo do pedido

### Etapa 7: Integração com E-mail via Intent

Implementado Intent ACTION_SENDTO para envio de pedidos por e-mail com assunto e corpo preenchidos automaticamente.

---

## CÓDIGO FONTE

### MainActivity.java
```java
package com.example.hamburgueriaz;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

public class MainActivity extends AppCompatActivity {

    private EditText editTextNome;
    private CheckBox checkBoxBacon, checkBoxQueijo, checkBoxOnionRings;
    private TextView textViewQuantidade, textViewResumo;
    private Button buttonMais, buttonMenos, buttonEnviarPedido;
    private int quantidade = 1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Inicializar views
        editTextNome = findViewById(R.id.editTextNome);
        checkBoxBacon = findViewById(R.id.checkBoxBacon);
        checkBoxQueijo = findViewById(R.id.checkBoxQueijo);
        checkBoxOnionRings = findViewById(R.id.checkBoxOnionRings);
        textViewQuantidade = findViewById(R.id.textViewQuantidade);
        textViewResumo = findViewById(R.id.textViewResumo);
        buttonMais = findViewById(R.id.buttonMais);
        buttonMenos = findViewById(R.id.buttonMenos);
        buttonEnviarPedido = findViewById(R.id.buttonEnviarPedido);

        // Configurar listeners
        buttonMais.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                somar();
            }
        });

        buttonMenos.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                subtrair();
            }
        });

        buttonEnviarPedido.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                enviarPedido();
            }
        });

        // Atualizar preço inicial
        atualizarResumo();
    }

    private void somar() {
        quantidade++;
        textViewQuantidade.setText(String.valueOf(quantidade));
        atualizarResumo();
    }

    private void subtrair() {
        if (quantidade > 1) {
            quantidade--;
            textViewQuantidade.setText(String.valueOf(quantidade));
            atualizarResumo();
        }
    }

    private double calcularPrecoTotal() {
        double precoBase = 20.0; // Preço do hambúrguer
        double precoAdicionais = 0.0;

        if (checkBoxBacon.isChecked()) {
            precoAdicionais += 2.0;
        }
        if (checkBoxQueijo.isChecked()) {
            precoAdicionais += 2.0;
        }
        if (checkBoxOnionRings.isChecked()) {
            precoAdicionais += 3.0;
        }

        return (precoBase + precoAdicionais) * quantidade;
    }

    private void atualizarResumo() {
        double precoTotal = calcularPrecoTotal();
        textViewResumo.setText(String.format("Preço total: R$ %.2f", precoTotal));
    }

    private void enviarPedido() {
        String nomeCliente = editTextNome.getText().toString().trim();
        
        if (nomeCliente.isEmpty()) {
            editTextNome.setError("Digite seu nome");
            return;
        }

        // Criar resumo do pedido
        StringBuilder resumo = new StringBuilder();
        resumo.append("Nome do cliente: ").append(nomeCliente).append("\n");
        resumo.append("Tem Bacon? ").append(checkBoxBacon.isChecked() ? "Sim" : "Não").append("\n");
        resumo.append("Tem Queijo? ").append(checkBoxQueijo.isChecked() ? "Sim" : "Não").append("\n");
        resumo.append("Tem Onion Rings? ").append(checkBoxOnionRings.isChecked() ? "Sim" : "Não").append("\n");
        resumo.append("Quantidade: ").append(quantidade).append("\n");
        resumo.append("Preço final: R$ ").append(String.format("%.2f", calcularPrecoTotal()));

        // Criar Intent para envio de e-mail
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
        emailIntent.setData(Uri.parse("mailto:"));
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, "Pedido de " + nomeCliente);
        emailIntent.putExtra(Intent.EXTRA_TEXT, resumo.toString());

        if (emailIntent.resolveActivity(getPackageManager()) != null) {
            startActivity(emailIntent);
        }
    }
}
```

### activity_main.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/imageViewLogo"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:src="@drawable/logo_hamburgueria"
            android:scaleType="centerCrop"
            android:layout_marginBottom="20dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FAÇA SEU PEDIDO"
            style="@style/EstiloTexto"
            android:layout_gravity="center"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextNome"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Digite seu nome"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ADICIONAIS:"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <CheckBox
            android:id="@+id/checkBoxBacon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Bacon (+R$ 2,00)" />

        <CheckBox
            android:id="@+id/checkBoxQueijo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Queijo (+R$ 2,00)" />

        <CheckBox
            android:id="@+id/checkBoxOnionRings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Onion Rings (+R$ 3,00)"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="QUANTIDADE"
            style="@style/EstiloTexto" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/buttonMenos"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:text="-"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/textViewQuantidade"
                android:layout_width="60dp"
                android:layout_height="50dp"
                android:text="1"
                android:textSize="18sp"
                android:gravity="center"
                android:layout_marginHorizontal="16dp" />

            <Button
                android:id="@+id/buttonMais"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:text="+"
                android:textSize="20sp" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="RESUMO DO PEDIDO"
            style="@style/EstiloTexto" />

        <TextView
            android:id="@+id/textViewResumo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Preço total: R$ 20,00"
            style="@style/EstiloTexto"
            android:layout_marginBottom="20dp" />

        <Button
            android:id="@+id/buttonEnviarPedido"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="ENVIAR PEDIDO"
            android:textSize="16sp" />

    </LinearLayout>
</ScrollView>
```

### themes.xml
```xml
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.HamburgueriaZ" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="EstiloTexto">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textSize">15sp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>
</resources>
```

---

## RESULTADOS OBTIDOS

### Funcionalidades Implementadas:

1. **Interface Responsiva**: Layout adaptável com ScrollView para diferentes tamanhos de tela
2. **Validação de Entrada**: Verificação de nome obrigatório
3. **Cálculo Dinâmico**: Preço atualizado automaticamente conforme seleções
4. **Controle de Quantidade**: Botões funcionais com validação de quantidade mínima
5. **Integração Externa**: Intent para envio de e-mail funcionando corretamente

### Preços Implementados:
- Hambúrguer base: R$ 20,00
- Bacon: +R$ 2,00
- Queijo: +R$ 2,00
- Onion Rings: +R$ 3,00

### Checklist Completo:
✅ Acessar o Android Studio  
✅ Criar um novo projeto  
✅ Criar a interface do app HamburgueriaZ  
✅ Realizar ajustes e incrementos na interface  
✅ Construir as funções "somar" e "subtrair"  
✅ Construir a função "enviarPedido"  
✅ Criar o objeto Intent para e-mail  
✅ Exportar o projeto para arquivo .zip  

---

## CONCLUSÃO

O projeto HamburgueriaZ foi desenvolvido com sucesso, cumprindo todos os objetivos propostos no roteiro da aula prática. O aplicativo demonstra conhecimentos fundamentais de desenvolvimento Android, incluindo:

- **Criação de interfaces** com XML e componentes nativos
- **Programação em Java** para Android com listeners e validações
- **Uso de Intents** para integração com aplicativos externos
- **Aplicação de estilos** para padronização visual
- **Gerenciamento de estado** da aplicação

O resultado final é uma aplicação funcional que pode ser utilizada como base para projetos mais complexos, demonstrando domínio das tecnologias e conceitos abordados na disciplina de Desenvolvimento Mobile.

**Projeto exportado como:** HamburgueriaZ.zip
