# HamburgueriaZ - Aplicativo Android

## Descrição
Aplicativo Android desenvolvido para uma hamburgueria, permitindo que os clientes realizem pedidos diretamente pelo app sem a necessidade de aplicativos de terceiros.

## Funcionalidades Implementadas

### ✅ Etapa 1: Criação do Projeto
- Projeto criado no Android Studio
- Nome: HamburgueriaZ
- Linguagem: Java
- SDK mínimo: API 23

### ✅ Etapa 2: Interface Inicial
- Campo para inserir nome do cliente
- Lista de checkboxes para adicionais (Bacon, Queijo, Onion Rings)
- Seleção de quantidade com botões + e -
- Exibição do preço total
- Botão para enviar pedido

### ✅ Etapa 3: Estilos de Texto
- Estilo personalizado "EstiloTexto" criado no themes.xml
- Propriedades aplicadas:
  - layout_width e layout_height: wrap_content
  - gravity: center_vertical
  - textAllCaps: true
  - textSize: 15sp
  - padding superior e inferior: 16dp

### ✅ Etapa 4: Adi<PERSON> de Imagens
- Logo da hamburgueria adicionada no topo
- ImageView configurada como banner
- Imagem vetorial personalizada criada

### ✅ Etapa 5: Funcionalidades de Quantidade
- Função somar() implementada
- Função subtrair() implementada
- Proteção contra quantidades negativas
- Atualização automática da quantidade na tela

### ✅ Etapa 6: Função Enviar Pedido
- Identificação do nome do cliente
- Verificação dos adicionais selecionados
- Cálculo automático do preço total
- Geração do resumo do pedido com formato:
  - Nome do cliente
  - Tem Bacon? Sim/Não
  - Tem Queijo? Sim/Não
  - Tem Onion Rings? Sim/Não
  - Quantidade: ___
  - Preço final: R$ ___

### ✅ Etapa 7: Implementação de Intents
- Intent ACTION_SENDTO para e-mail
- Assunto automático: "Pedido de [nome do cliente]"
- Corpo do e-mail com resumo completo do pedido
- Verificação de aplicativos de e-mail disponíveis

## Preços
- Hambúrguer base: R$ 20,00
- Bacon: +R$ 2,00
- Queijo: +R$ 2,00
- Onion Rings: +R$ 3,00

## Estrutura do Projeto
```
HamburgueriaZ/
├── app/
│   ├── src/main/
│   │   ├── java/com/example/hamburgueriaz/
│   │   │   └── MainActivity.java
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml
│   │   │   ├── values/
│   │   │   │   ├── colors.xml
│   │   │   │   ├── strings.xml
│   │   │   │   └── themes.xml
│   │   │   └── drawable/
│   │   │       └── logo_hamburgueria.xml
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── build.gradle
├── settings.gradle
└── gradle.properties
```

## Como Executar
1. Abrir o projeto no Android Studio
2. Sincronizar o projeto (Sync Project)
3. Executar em emulador ou dispositivo físico
4. Testar todas as funcionalidades

## Tecnologias Utilizadas
- Android Studio Flamingo | 2022.2.1
- Java JDK 20
- Android SDK API 23+
- Material Design Components

## Autor
Desenvolvido seguindo o roteiro de aula prática de Desenvolvimento Mobile.
