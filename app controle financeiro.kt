package com.example.financecontrol.ui.reports

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.financecontrol.data.TransactionDao
import com.example.financecontrol.data.model.Transaction
import com.example.financecontrol.data.model.TransactionType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class ReportsViewModel @Inject constructor(
    private val transactionDao: TransactionDao
) : ViewModel() {

    private val _period = MutableStateFlow(Period.MONTH)
    val period: StateFlow<Period> = _period.asStateFlow()

    private val _transactions = MutableStateFlow<List<Transaction>>(emptyList())
    val transactions: StateFlow<List<Transaction>> = _transactions.asStateFlow()

    init {
        viewModelScope.launch {
            transactionDao.getAllTransactions()
                .collect { transactions ->
                    _transactions.value = transactions
                }
        }
    }

    fun setPeriod(period: Period) {
        _period.value = period
    }

    val incomeExpenseData: StateFlow<Pair<Double, Double>> = combine(
        transactions,
        period
    ) { transactions, period ->
        val filteredTransactions = filterTransactionsByPeriod(transactions, period)
        val income = filteredTransactions
            .filter { it.type == TransactionType.INCOME }
            .sumOf { it.amount }
        val expense = filteredTransactions
            .filter { it.type == TransactionType.EXPENSE }
            .sumOf { it.amount }
        income to expense
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0.0 to 0.0)

    val categoryData: StateFlow<List<Pair<String, Double>>> = combine(
        transactions,
        period
    ) { transactions, period ->
        val filteredTransactions = filterTransactionsByPeriod(transactions, period)
        filteredTransactions
            .filter { it.type == TransactionType.EXPENSE }
            .groupBy { it.category }
            .mapValues { (_, transactions) -> transactions.sumOf { it.amount } }
            .toList()
            .sortedByDescending { it.second }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())

    private fun filterTransactionsByPeriod(
        transactions: List<Transaction>,
        period: Period
    ): List<Transaction> {
        val calendar = Calendar.getInstance()
        val now = Date()

        return when (period) {
            Period.WEEK -> {
                calendar.time = now
                calendar.add(Calendar.DAY_OF_YEAR, -7)
                transactions.filter { it.date.after(calendar.time) }
            }
            Period.MONTH -> {
                calendar.time = now
                calendar.add(Calendar.MONTH, -1)
                transactions.filter { it.date.after(calendar.time) }
            }
            Period.YEAR -> {
                calendar.time = now
                calendar.add(Calendar.YEAR, -1)
                transactions.filter { it.date.after(calendar.time) }
            }
        }
    }

    enum class Period {
        WEEK, MONTH, YEAR
    }
} 