#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para converter o trabalho de Markdown para Word
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import re

def criar_documento_word():
    # Criar novo documento
    doc = Document()
    
    # Configurar margens
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # Título principal
    titulo = doc.add_heading('TRABALHO PRÁTICO - DESENVOLVIMENTO MOBILE', 0)
    titulo.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitulo = doc.add_heading('APLICATIVO HAMBURGUERIAZ', 1)
    subtitulo.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Linha separadora
    doc.add_paragraph('_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Informações do trabalho
    info_para = doc.add_paragraph()
    info_para.add_run('Disciplina: ').bold = True
    info_para.add_run('Desenvolvimento Mobile\n')
    info_para.add_run('Objetivo: ').bold = True
    info_para.add_run('Desenvolver aplicativo Android completo seguindo roteiro de aula prática\n')
    info_para.add_run('Linguagem: ').bold = True
    info_para.add_run('Java\n')
    info_para.add_run('IDE: ').bold = True
    info_para.add_run('Android Studio\n')
    info_para.add_run('Data: ').bold = True
    info_para.add_run('2024')
    
    # Linha separadora
    doc.add_paragraph('_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Sumário
    doc.add_heading('SUMÁRIO', 1)
    sumario_items = [
        '1. Introdução',
        '2. Objetivos',
        '3. Infraestrutura Utilizada',
        '4. Desenvolvimento do Projeto',
        '5. Implementação das Funcionalidades',
        '6. Resultados Obtidos',
        '7. Conclusão',
        '8. Anexos - Código Fonte'
    ]
    
    for item in sumario_items:
        doc.add_paragraph(item, style='List Number')
    
    # Quebra de página
    doc.add_page_break()
    
    # 1. INTRODUÇÃO
    doc.add_heading('1. INTRODUÇÃO', 1)
    intro_text = """Este trabalho apresenta o desenvolvimento completo de um aplicativo Android para uma hamburgueria fictícia chamada "HamburgueriaZ". O projeto foi desenvolvido seguindo um roteiro estruturado de aula prática, implementando desde a interface básica até funcionalidades avançadas como integração com aplicativos externos através de Intents.

O aplicativo permite que clientes realizem pedidos de hambúrgueres diretamente pelo smartphone, escolhendo adicionais, quantidade e enviando o pedido por e-mail, eliminando a necessidade de aplicativos de terceiros."""
    
    doc.add_paragraph(intro_text)
    
    # 2. OBJETIVOS
    doc.add_heading('2. OBJETIVOS', 1)
    
    doc.add_heading('2.1 Objetivos Gerais', 2)
    objetivos_gerais = [
        'Desenvolver competências em desenvolvimento mobile para Android',
        'Aplicar conceitos de interface de usuário (UI) e experiência do usuário (UX)',
        'Implementar funcionalidades em Java para Android',
        'Utilizar Intents para integração com aplicativos externos'
    ]
    
    for obj in objetivos_gerais:
        doc.add_paragraph(obj, style='List Bullet')
    
    doc.add_heading('2.2 Objetivos Específicos', 2)
    objetivos_especificos = [
        'Saber utilizar o software Android Studio',
        'Construir interface de aplicação Android com estilos predefinidos e imagens',
        'Implementar funcionalidades de um app na linguagem Java',
        'Utilizar Intents para interação com aplicativos externos',
        'Exportar projeto do Android Studio'
    ]
    
    for obj in objetivos_especificos:
        doc.add_paragraph(obj, style='List Bullet')
    
    # 3. INFRAESTRUTURA UTILIZADA
    doc.add_heading('3. INFRAESTRUTURA UTILIZADA', 1)
    
    doc.add_heading('3.1 Software Principal', 2)
    software_para = doc.add_paragraph()
    software_para.add_run('Android Studio Flamingo | 2022.2.1\n').bold = True
    software_para.add_run('• Ambiente de desenvolvimento integrado (IDE) oficial para desenvolvimento Android\n')
    software_para.add_run('• Baseado no IntelliJ IDEA\n')
    software_para.add_run('• Ferramentas avançadas de desenvolvimento\n')
    software_para.add_run('• Editor de código com recursos de autocomplete e debugging')
    
    doc.add_heading('3.2 Pré-requisitos', 2)
    prereq_para = doc.add_paragraph()
    prereq_para.add_run('Java JDK 20\n').bold = True
    prereq_para.add_run('• Ambiente de desenvolvimento para construção de aplicativos Java\n')
    prereq_para.add_run('• Necessário para compilação do código Android')
    
    doc.add_heading('3.3 Configurações do Projeto', 2)
    config_items = [
        'SDK Mínimo: API 23 (Android 6.0)',
        'SDK de Compilação: API 34 (Android 14)',
        'Linguagem: Java',
        'Tipo de Licença: Freeware'
    ]
    
    for config in config_items:
        doc.add_paragraph(config, style='List Bullet')
    
    # 4. DESENVOLVIMENTO DO PROJETO
    doc.add_heading('4. DESENVOLVIMENTO DO PROJETO', 1)
    
    # Etapas do desenvolvimento
    etapas = [
        {
            'titulo': 'ETAPA 1: CRIAÇÃO DO PROJETO',
            'conteudo': """O projeto foi iniciado no Android Studio seguindo os seguintes passos:

1. Criação do Novo Projeto
   • File > New > New Project
   • Template: Empty Activity
   • Nome: HamburgueriaZ
   • Linguagem: Java
   • SDK Mínimo: API 23

2. Estrutura Inicial Gerada
   • Arquivos de configuração criados automaticamente
   • Estrutura de pastas organizada
   • Manifesto Android configurado"""
        },
        {
            'titulo': 'ETAPA 2: DESENVOLVIMENTO DA INTERFACE INICIAL',
            'conteudo': """A interface foi desenvolvida com foco na usabilidade e experiência do usuário, implementando todos os requisitos especificados:

Componentes Implementados:

1. Campo Nome do Cliente
   • EditText para entrada de texto
   • Validação obrigatória
   • Hint explicativo

2. Seleção de Adicionais
   • CheckBox para Bacon (+R$ 2,00)
   • CheckBox para Queijo (+R$ 2,00)
   • CheckBox para Onion Rings (+R$ 3,00)
   • Atualização automática do preço

3. Controle de Quantidade
   • Botão "-" para diminuir
   • TextView central mostrando quantidade
   • Botão "+" para aumentar
   • Proteção contra valores negativos

4. Exibição de Informações
   • TextView para preço total
   • TextView para resumo do pedido
   • Atualização em tempo real

5. Ação Principal
   • Botão "Enviar Pedido"
   • Integração com Intent de e-mail"""
        },
        {
            'titulo': 'ETAPA 3: PADRONIZAÇÃO DE ESTILOS',
            'conteudo': """Foi criado um estilo personalizado para padronizar a aparência dos textos:

Estilo "EstiloTexto" Implementado:
• layout_width e layout_height: wrap_content
• gravity: center_vertical
• textAllCaps: true
• textSize: 15sp
• paddingTop e paddingBottom: 16dp
• textStyle: bold
• textColor: #333333

Aplicação do Estilo:
• TextView "Faça seu pedido"
• TextView "Quantidade"
• TextView "Resumo do pedido"
• TextView do valor total"""
        },
        {
            'titulo': 'ETAPA 4: ADIÇÃO DE IMAGENS',
            'conteudo': """Foi implementada uma ImageView no topo da aplicação funcionando como banner:

Características da Imagem:
• Logo vetorial personalizada da HamburgueriaZ
• Posicionamento no topo da tela
• Estilo de banner responsivo
• Cores harmoniosas com o tema do app"""
        }
    ]
    
    for etapa in etapas:
        doc.add_heading(etapa['titulo'], 2)
        doc.add_paragraph(etapa['conteudo'])
    
    # Quebra de página
    doc.add_page_break()
    
    # 5. IMPLEMENTAÇÃO DAS FUNCIONALIDADES
    doc.add_heading('5. IMPLEMENTAÇÃO DAS FUNCIONALIDADES', 1)
    
    funcionalidades = [
        {
            'titulo': 'ETAPA 5: FUNCIONALIDADES DE QUANTIDADE',
            'conteudo': """Implementação dos métodos para controle de quantidade:

Método somar():
• Incrementa a quantidade
• Atualiza a exibição na tela
• Recalcula o preço total

Método subtrair():
• Decrementa a quantidade (mínimo 1)
• Proteção contra valores negativos
• Feedback visual para o usuário
• Atualização automática do preço

Características Implementadas:
• Incremento e decremento da quantidade
• Proteção contra valores negativos
• Atualização automática do preço
• Feedback visual para o usuário"""
        },
        {
            'titulo': 'ETAPA 6: FUNÇÃO ENVIAR PEDIDO',
            'conteudo': """Funcionalidades Implementadas:

1. Identificação do Cliente
   • Captura do nome digitado
   • Validação de campo obrigatório

2. Verificação de Adicionais
   • Verificação do estado dos CheckBoxes
   • Identificação dos itens selecionados

3. Cálculo do Preço Total
   • Preço base: R$ 20,00
   • Bacon: +R$ 2,00
   • Queijo: +R$ 2,00
   • Onion Rings: +R$ 3,00
   • Multiplicação pela quantidade

4. Geração do Resumo
   • Nome do cliente
   • Adicionais selecionados (Sim/Não)
   • Quantidade
   • Preço final formatado"""
        },
        {
            'titulo': 'ETAPA 7: IMPLEMENTAÇÃO DE INTENTS',
            'conteudo': """Intent para E-mail implementado com as seguintes características:

• Tipo: ACTION_SENDTO
• Protocolo: mailto:
• Assunto automático: "Pedido de [nome do cliente]"
• Corpo: Resumo completo do pedido
• Verificação de disponibilidade de apps de e-mail
• Tratamento de erro quando não há app disponível

Funcionalidade:
Ao clicar em "Enviar Pedido", o aplicativo abre automaticamente o app de e-mail padrão do dispositivo com o assunto e corpo já preenchidos com as informações do pedido."""
        }
    ]
    
    for func in funcionalidades:
        doc.add_heading(func['titulo'], 2)
        doc.add_paragraph(func['conteudo'])
    
    # 6. RESULTADOS OBTIDOS
    doc.add_heading('6. RESULTADOS OBTIDOS', 1)
    
    doc.add_heading('6.1 Funcionalidades Implementadas com Sucesso', 2)
    resultados = [
        '✅ Interface Completa - Layout responsivo e intuitivo',
        '✅ Lógica de Negócio - Cálculo correto de preços e validações',
        '✅ Integração Externa - Intent funcionando corretamente',
        '✅ Experiência do Usuário - Feedback visual adequado'
    ]
    
    for resultado in resultados:
        doc.add_paragraph(resultado, style='List Bullet')
    
    doc.add_heading('6.2 Testes Realizados', 2)
    testes = [
        'Teste de Interface - Todos os componentes respondem adequadamente',
        'Teste de Funcionalidades - Cálculo de preços e controle de quantidade',
        'Teste de Integração - Intent abre aplicativo de e-mail corretamente'
    ]
    
    for teste in testes:
        doc.add_paragraph(teste, style='List Bullet')
    
    # 7. CONCLUSÃO
    doc.add_heading('7. CONCLUSÃO', 1)
    
    conclusao_text = """O desenvolvimento do aplicativo HamburgueriaZ foi concluído com sucesso, atendendo a todos os objetivos propostos no roteiro de aula prática. O projeto demonstrou a aplicação prática de conceitos fundamentais do desenvolvimento Android.

Competências Desenvolvidas:

1. Desenvolvimento de Interface
   • Criação de layouts responsivos
   • Aplicação de estilos personalizados
   • Uso adequado de componentes Android

2. Programação em Java para Android
   • Implementação de lógica de negócio
   • Manipulação de eventos
   • Gerenciamento de estado da aplicação

3. Integração com Sistema
   • Uso de Intents para comunicação entre apps
   • Tratamento de casos de erro
   • Validação de disponibilidade de recursos

4. Boas Práticas
   • Código bem estruturado e comentado
   • Separação de responsabilidades
   • Tratamento adequado de exceções

O aplicativo HamburgueriaZ representa uma solução completa para pedidos de hamburgueria, demonstrando como conceitos teóricos de desenvolvimento mobile podem ser aplicados na prática para resolver problemas reais de negócio."""
    
    doc.add_paragraph(conclusao_text)
    
    # Resultados finais
    doc.add_heading('Resultados Alcançados:', 2)
    resultados_finais = [
        '✅ Aplicativo funcional e completo',
        '✅ Interface intuitiva e atrativa',
        '✅ Todas as funcionalidades implementadas',
        '✅ Integração externa funcionando',
        '✅ Código bem documentado',
        '✅ Projeto exportável'
    ]
    
    for resultado in resultados_finais:
        doc.add_paragraph(resultado, style='List Bullet')
    
    # Quebra de página
    doc.add_page_break()
    
    # 8. ANEXOS
    doc.add_heading('8. ANEXOS - ESTRUTURA DO PROJETO', 1)
    
    estrutura_text = """Estrutura Final do Projeto:

HamburgueriaZ/
├── app/
│   ├── src/main/
│   │   ├── java/com/example/hamburgueriaz/
│   │   │   └── MainActivity.java (200+ linhas)
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml
│   │   │   ├── values/
│   │   │   │   ├── colors.xml
│   │   │   │   ├── strings.xml
│   │   │   │   └── themes.xml
│   │   │   └── drawable/
│   │   │       └── logo_hamburgueria.xml
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── build.gradle
├── settings.gradle
├── gradle.properties
└── README.md

Arquivos Principais Criados:
• MainActivity.java - Lógica principal da aplicação
• activity_main.xml - Layout da interface
• themes.xml - Estilos personalizados
• AndroidManifest.xml - Configurações do app
• build.gradle - Dependências e configurações de build"""
    
    doc.add_paragraph(estrutura_text)
    
    # Rodapé
    doc.add_paragraph('\n' + '_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    rodape = doc.add_paragraph()
    rodape.alignment = WD_ALIGN_PARAGRAPH.CENTER
    rodape.add_run('Trabalho desenvolvido seguindo roteiro de aula prática de Desenvolvimento Mobile\n').bold = True
    rodape.add_run('Todas as etapas foram implementadas com sucesso\n').bold = True
    rodape.add_run('Projeto pronto para uso e expansão futura').bold = True
    
    # Salvar documento
    doc.save('Trabalho_Desenvolvimento_Mobile_HamburgueriaZ.docx')
    print("Documento Word criado com sucesso: Trabalho_Desenvolvimento_Mobile_HamburgueriaZ.docx")

if __name__ == "__main__":
    try:
        criar_documento_word()
    except ImportError:
        print("Erro: Biblioteca python-docx não encontrada.")
        print("Para instalar, execute: pip install python-docx")
    except Exception as e:
        print(f"Erro ao criar documento: {e}")
