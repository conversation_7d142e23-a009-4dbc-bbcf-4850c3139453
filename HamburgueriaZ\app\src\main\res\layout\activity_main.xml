<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Logo da Hamburgueria -->
    <ImageView
        android:id="@+id/imageViewLogo"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_marginBottom="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/logo_hamburgueria"
        android:contentDescription="Logo HamburgueriaZ" />

    <!-- Título -->
    <TextView
        android:id="@+id/textViewTitulo"
        style="@style/EstiloTexto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FAÇA SEU PEDIDO"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center" />

    <!-- Campo para nome do cliente -->
    <EditText
        android:id="@+id/editTextNome"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Digite seu nome"
        android:inputType="textPersonName" />

    <!-- Seção de Adicionais -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Adicionais:"
        android:textSize="16sp"
        android:textStyle="bold" />

    <CheckBox
        android:id="@+id/checkBoxBacon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Bacon (+R$ 2,00)" />

    <CheckBox
        android:id="@+id/checkBoxQueijo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Queijo (+R$ 2,00)" />

    <CheckBox
        android:id="@+id/checkBoxOnionRings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Onion Rings (+R$ 3,00)" />

    <!-- Seção de Quantidade -->
    <TextView
        android:id="@+id/textViewQuantidade"
        style="@style/EstiloTexto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="QUANTIDADE" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/buttonSubtrair"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:text="-"
            android:textSize="24sp"
            android:onClick="subtrair" />

        <TextView
            android:id="@+id/textViewQuantidadeValor"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:text="1"
            android:textSize="24sp"
            android:gravity="center"
            android:layout_marginHorizontal="16dp" />

        <Button
            android:id="@+id/buttonSomar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:text="+"
            android:textSize="24sp"
            android:onClick="somar" />

    </LinearLayout>

    <!-- Resumo do Pedido -->
    <TextView
        android:id="@+id/textViewResumoTitulo"
        style="@style/EstiloTexto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="RESUMO DO PEDIDO" />

    <TextView
        android:id="@+id/textViewResumo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text=""
        android:textSize="14sp"
        android:background="#f0f0f0"
        android:padding="12dp" />

    <!-- Preço Total -->
    <TextView
        android:id="@+id/textViewPrecoTotal"
        style="@style/EstiloTexto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="PREÇO TOTAL: R$ 20,00"
        android:textStyle="bold"
        android:textColor="#2E7D32" />

    <!-- Botão Enviar Pedido -->
    <Button
        android:id="@+id/buttonEnviarPedido"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="ENVIAR PEDIDO"
        android:textSize="16sp"
        android:backgroundTint="#FF5722"
        android:textColor="@android:color/white"
        android:onClick="enviarPedido" />

</LinearLayout>
